package com.yxt.talent.rv.controller.manage.meet.log;

import com.alibaba.fastjson.JSON;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.controller.manage.meet.command.NewCaliMeetUserDelCmd;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Component
@RequiredArgsConstructor
public class CaliUserExportProvider implements AuditLogDataProvider<String, String> {
    private final CalimeetMapper calimeetMapper;

    @Override
    public String before(String param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public String after(String param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public String convertParam(Object param, AuditLogBasicBean logBasic) {
        if (Objects.isNull(param)) {
            return null;
        }
        return param.toString();
    }

    @Override
    public Pair<String, String> entityInfo(String param, String beforeObj, String afterObj,
            AuditLogBasicBean logBasic) {
        String caliMeetName = "";
        if (StringUtils.isNotBlank(param)) {
            CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(param, logBasic.getOrgId());
            if (Objects.nonNull(calimeetPO)) {
                caliMeetName = calimeetPO.getCalimeetName();
            }
        }
        return Pair.of(param, String.format("盘点校准-%s-导出校准人员", caliMeetName));
    }
}
