package com.yxt.talent.rv.application.xpd.result;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.export.I18nComponent;
import com.yxt.spsdfacade.bean.spsd.IndicatorDto;
import com.yxt.spsdfacade.bean.spsd.ModelBase4Facade;
import com.yxt.spsdk.common.bean.*;
import com.yxt.spsdk.common.component.ExpressionCalc;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.spsdk.common.exception.ExpressNullValueException;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.spsdk.common.utils.IArrayUtils;
import com.yxt.spsdk.common.utils.SpCallUtils;
import com.yxt.talent.rv.application.calimeet.dto.CaliUserIdDto;
import com.yxt.talent.rv.application.calimeet.dto.CalimeetBriefDto;
import com.yxt.talent.rv.application.xpd.aom.XpdAomService;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.*;
import com.yxt.talent.rv.application.xpd.rule.XpdRuleConfAppService;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.*;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.PerfEvalTypeEnum;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.cache.CommonCacheRepository;
import com.yxt.talent.rv.infrastructure.persistence.cache.RedisRepo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.repository.activity.ActivityObjectiveResultRepo;
import com.yxt.talent.rv.infrastructure.repository.activity.BaseActivityResultRepo;
import com.yxt.talent.rv.infrastructure.repository.aom.RvActivityParticipationMemberRepo;
import com.yxt.talent.rv.infrastructure.repository.spmodel.DwdUserIndicatorResultRepo;
import com.yxt.talent.rv.infrastructure.repository.xpd.*;
import com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import jakarta.annotation.Nullable;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.controller.manage.xpd.rule.enums.ScoreSystemEnum.getMaxScore;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.ScoreSystemUtil.convertToTargetSystem;

/**
 * @Author: geyan
 * @Date: 9/12/24 15:20
 * @Description:
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class XpdResultCalcService {
    private static final MathContext MC_TWO = new MathContext(2);
    private static final Set emptySet = Collections.emptySet();
    private static final int CALC_BATCH_COUNT = 300;
    private static final int CALC_USER = 0;
    private static final int CALC_TOTAL_SCORE = 1;
    private static final int CALC_STANDARD_SCORE = 2;

    private final XpdMapper xpdMapper;
    private final XpdDimMapper xpdDimMapper;
    private final XpdDimRuleMapper xpdDimRuleMapper;
    private final XpdDimRuleCalcMapper xpdDimRuleCalcMapper;
    private final XpdCalcLogRepo xpdCalcLogRepo;
    private final XpdRuleConfMapper xpdRuleConfMapper;
    private final XpdResultUserIndicatorMapper xpdResultUserIndicatorMapper;
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final XpdResultIndicatorMapper xpdResultIndicatorMapper;
    private final RvActivityParticipationMemberRepo rvActivityParticipationMemberRepo;
    private final XpdRuleMapper xpdRuleMapper;
    private final XpdRuleCalcDimMapper xpdRuleCalcDimMapper;
    private final XpdRuleCalcIndicatorMapper xpdRuleCalcIndicatorMapper;
    private final XpdResultUserMapper xpdResultUserMapper;
    private final XpdLevelMapper xpdLevelMapper;
    private final XpdImportMapper xpdImportMapper;
    private final XpdImportDimUserMapper xpdImportDimUserMapper;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final SpRuleService spRuleService;
    private final XpdAomService xpdAomService;
    private final XpdImportIndicatorUserRepo xpdImportIndicatorUserRepo;
    private final BaseActivityResultRepo baseActivityResultRepo;
    private final ActivityObjectiveResultRepo activityObjectiveResultRepo;
    private final SpsdAclService spsdAclService;
    private final SpevalAclService spevalAclService;
    private final DwdUserIndicatorResultRepo dwdUserIndicatorResultRepo;
    private final RedisRepo redisRepo;
    private final I18nComponent i18nComponent;
    private final XpdResultUserDimcombMapper xpdResultUserDimcombMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final XpdGridMapper xpdGridMapper;
    private final XpdGridCellMapper xpdGridCellMapper;
    private final CalimeetMapper calimeetMapper;
    private final CalimeetUserMapper calimeetUserMapper;
    private final CalimeetRecordMapper calimeetRecordMapper;
    private final CalimeetRecordItemMapper calimeetRecordItemMapper;
    private final CalimeetResultUserDimMapper calimeetResultUserDimMapper;
    private final CalimeetResultUserIndicatorMapper calimeetResultUserIndicatorMapper;
    private final CalimeetResultUserMapper calimeetResultUserMapper;
    private final CommonCacheRepository cacheRepository;

    public XpdFormulaAllArgDto formulaAllArg(UserBasicInfo userInfo, String xpdId) {
        String orgId = userInfo.getOrgId();
        XpdPO xpdPO = xpdMapper.selectByIdAndOrg(orgId, xpdId);
        if (xpdPO == null) {
            return null;
        }
        //得分
        String scoreI18n = i18nComponent.getI18nValue("apis.sptalentrv.xpd.formula.score", userInfo.getLocale());
        //导入数据
        String importI18n = i18nComponent.getI18nValue("apis.sptalentrv.xpd.formula.import", userInfo.getLocale());
        //个人档案数据
        String profileI18n = i18nComponent.getI18nValue("apis.sptalentrv.xpd.formula.profile", userInfo.getLocale());
        XpdFormulaAllArgDto argDto = new XpdFormulaAllArgDto();
        List<IndicatorDto> indicators = spsdAclService.getLastIndicators(orgId, xpdPO.getModelId());
        Map<String, IndicatorDto> indicatorMap = StreamUtil.list2map(indicators, IndicatorDto::getItemId);
        Set<String> evalTypeIdSet = new HashSet<>();
        List<AomActvExtBO> actvExtList = xpdAomService.activityIndicatorList(orgId, xpdPO.getAomPrjId());
        actvExtList.forEach(actv -> {
            evalTypeIdSet.addAll(Optional.ofNullable(actv.getEvalTypes()).orElse(Lists.emptyList()));
        });
        Map<String, String> evalTypeIdMap = spevalAclService.getTypeStrIdName(orgId, userInfo.getLocale(), evalTypeIdSet);
        SpEvalTypeEnum.putTypeIdName(evalTypeIdMap, evalTypeIdSet, userInfo.getLocale());
        actvExtList.forEach(actv -> {
            boolean isEval = UacdTypeEnum.ACTV_SPEVAL.getRegId().equals(actv.getRefRegId());
            boolean hasLevelActv = UacdTypeEnum.hasLevelActv(actv.getRefRegId());
            XpdFormulaAllArgDto.ArgRefRoot refRoot = new XpdFormulaAllArgDto.ArgRefRoot();
            refRoot.setRefType(DimRuleCalcRefEnum.AOM_ACT.getCode());
            refRoot.setRefId(actv.getActvRefId());
            refRoot.setRefRegId(actv.getRefRegId());
            refRoot.setRefName(actv.getActvRefName());
            refRoot.setRefIndicators(BeanCopierUtil.convertList(actv.getIndicators(), ind -> {
                return Optional.ofNullable(indicatorMap.get(ind.getSdIndicatorId()))
                    .map(indicator -> {
                        //有等级指标时，等级不相等时不返回
                        if (indicator.getMaxLevel() > 0 && hasLevelActv
                            && !Objects.equals(indicator.getStandardLevel(), ind.getStandardLevel())) {
                            return null;
                        }
                        XpdFormulaAllArgDto.ArgRefIndicator refIndicator = new XpdFormulaAllArgDto.ArgRefIndicator();
                        refIndicator.setSdIndicatorId(indicator.getItemId());
                        refIndicator.setSdIndicatorName(indicator.getIndicatorName());
                        if (isEval) {
                            List<XpdFormulaAllArgDto.ArgRefIndicatorArg> args = new ArrayList<>();
                            actv.getEvalTypes().forEach(evalTypeId -> {
                                String typeIdName = evalTypeIdMap.get(evalTypeId);
                                if (StringUtils.isNotEmpty(typeIdName)) {
                                    args.add(buildIndicatorArg(FormulaTypeEnum.AOM_ACTV_EVAL,
                                        new FormulaAomActvParam(actv.getActvRefId(), indicator.getItemId(), evalTypeId),
                                        typeIdName));
                                }
                            });
                            args.add(buildIndicatorArg(FormulaTypeEnum.AOM_ACTV_EVAL,
                                new FormulaAomActvParam(actv.getActvRefId(), indicator.getItemId(), SpEvalTypeEnum.COMPREHENSIVE.getCode()),
                                SpEvalTypeEnum.COMPREHENSIVE.getName()));
                            refIndicator.setChooseArgs(args);
                        } else {
                            refIndicator.setChooseArgs(Lists.newArrayList(buildIndicatorArg(FormulaTypeEnum.AOM_ACTV_OTHER,
                                new FormulaAomActvParam(actv.getActvRefId(), indicator.getItemId(), null), scoreI18n)));
                        }
                        return refIndicator;
                    })
                    .orElse(null);
            }));
            IArrayUtils.remove(refRoot.getRefIndicators(), Objects::isNull);
            if (!refRoot.getRefIndicators().isEmpty()) {
                //没有可选的指标
                argDto.getRefList().add(refRoot);
            }
        });

        List<XpdImportPO> importPOList = xpdImportMapper.selectByXpdId(orgId, xpdId, XpdImportTypeEnum.DIM_INDICATOR.getCode());
        Map<String, String> dimImportIdMap = IArrayUtils.listAsMap(importPOList,
            XpdImportPO::getSdDimId, XpdImportPO::getId);
        spsdAclService.dimIndicatorMapByDims(orgId, xpdPO.getModelId(),
            BeanCopierUtil.convertList(importPOList, XpdImportPO::getSdDimId)).forEach((sdDim, dimIndicators) -> {
            XpdFormulaAllArgDto.ArgRefRoot refRoot = new XpdFormulaAllArgDto.ArgRefRoot();
            String importId = dimImportIdMap.get(sdDim.getDmId());
            refRoot.setRefType(DimRuleCalcRefEnum.IMPORT_DATA.getCode());
            refRoot.setRefId(importId);
            refRoot.setRefName(importI18n + "-" + sdDim.getDmName());
            refRoot.setRefIndicators(BeanCopierUtil.convertList(dimIndicators, indicator -> {
                XpdFormulaAllArgDto.ArgRefIndicator refIndicator = new XpdFormulaAllArgDto.ArgRefIndicator();
                refIndicator.setSdIndicatorId(indicator.getItemId());
                refIndicator.setSdIndicatorName(indicator.getIndicatorName());
                refIndicator.setChooseArgs(Lists.newArrayList(buildIndicatorArg(FormulaTypeEnum.XPD_IMPORT,
                    new FormulaImportParam(importId, indicator.getItemId()), scoreI18n)));
                return refIndicator;
            }));
            argDto.getRefList().add(refRoot);
        });
        XpdFormulaAllArgDto.ArgRefRoot userArchiveRoot = new XpdFormulaAllArgDto.ArgRefRoot();
        userArchiveRoot.setRefType(DimRuleCalcRefEnum.PRI_PROFILE.getCode());
        userArchiveRoot.setRefId(StringPool.DASH);
        userArchiveRoot.setRefName(profileI18n);
        userArchiveRoot.setRefIndicators(BeanCopierUtil.convertList(indicators, indicator -> {
            XpdFormulaAllArgDto.ArgRefIndicator refIndicator = new XpdFormulaAllArgDto.ArgRefIndicator();
            refIndicator.setSdIndicatorId(indicator.getItemId());
            refIndicator.setSdIndicatorName(indicator.getIndicatorName());
            refIndicator.setChooseArgs(Lists.newArrayList(buildIndicatorArg(FormulaTypeEnum.PRI_PROFILE,
                indicator.getItemId(), scoreI18n)));
            return refIndicator;
        }));
        argDto.getRefList().add(userArchiveRoot);
        //去掉没有指标的
        IArrayUtils.remove(argDto.getRefList(), item -> CollectionUtils.isEmpty(item.getRefIndicators()));
        return argDto;
    }

    private XpdFormulaAllArgDto.ArgRefIndicatorArg buildIndicatorArg(FormulaTypeEnum formulaType, Object param, String name) {
        XpdFormulaAllArgDto.ArgRefIndicatorArg arg = new XpdFormulaAllArgDto.ArgRefIndicatorArg();
        arg.setArgKey(formulaType.buildParam(param));
        arg.setName(name);
        return arg;
    }

    /**
     * 计算指标总分（考虑了分制，如5分制最大分只会返回5分）
     * @param xpdPO
     * @param list
     */
    public void calcIndicatorTotalScoreByScoreSystem(XpdPO xpdPO, List<? extends XpdIndicatorCalcBase> list) {
        if (xpdPO == null || CollectionUtils.isEmpty(list)) {
            return;
        }
        CalcResultRefContext refContext = new CalcResultRefContext();
        refContext.orgId = xpdPO.getOrgId();
        refContext.xpdId = xpdPO.getId();
        refContext.aomPrjId = xpdPO.getAomPrjId();
        refContext.modelId = xpdPO.getModelId();
        refContext.aomActvMap = StreamUtil.list2map(
            xpdAomService.activityIndicatorList(refContext.orgId,refContext.aomPrjId),
            AomActvExtBO::getActvRefId);
        List<XpdImportPO> importPOList = xpdImportMapper.selectByXpdIdAndOrgId(refContext.orgId, refContext.xpdId);
        refContext.xpdImportMap = StreamUtil.list2map(importPOList, XpdImportPO::getId);
        Set<String> importIdSet = importPOList.stream()
            .filter(item -> XpdImportTypeEnum.DIM_INDICATOR.getCode().equals(item.getImportType()))
            .map(XpdImportPO::getId).collect(Collectors.toSet());
        for (XpdIndicatorCalcBase ruleCalcDto : list) {
            String sdIndicatorId = ruleCalcDto.getSdIndicatorId();
            refContext.addRefList(ruleCalcDto.getSdIndicatorId(), false, ruleCalcDto.getRefList());
            for (XpdDimRuleCalcRefDto refDto : Optional.ofNullable(ruleCalcDto.getRefList()).orElse(Lists.emptyList())) {
                if (refDto.getRefType() == DimRuleCalcRefEnum.AOM_ACT.getCode()) {
                    refDto.setRefIdNotExist(!refContext.aomActvMap.containsKey(refDto.getRefId()));
                    refDto.setNotRefIndicator(Optional.ofNullable(refContext.aomActvMap.get(refDto.getRefId()))
                        .map(AomActvExtBO::getIndicators)
                        .map(item -> IArrayUtils.getFirstMatch(item, refInd -> Objects.equals(sdIndicatorId, refInd.getSdIndicatorId())) == null)
                        .orElse(true));
                } else if (refDto.getRefType() == DimRuleCalcRefEnum.IMPORT_DATA.getCode()) {
                    refDto.setRefIdNotExist(!importIdSet.contains(refDto.getRefId()));
                }
            }
        }
        prepareRefContext(refContext);

        XpdRuleConfPO xpdRuleConfPO = xpdRuleConfMapper.selectByXpdId(xpdPO.getOrgId(), xpdPO.getId());
        Validate.isNotNull(xpdRuleConfPO, ExceptionKeys.XPD_RULE_CONF_NOT_FOUND);
        Integer scoreSystem = xpdRuleConfPO.getScoreSystem();
        for (XpdIndicatorCalcBase ruleCalcDto : list) {
            IndicatorResult result = calcIndicatorScoreResult(refContext, ruleCalcDto.getSdIndicatorId(),
                ruleCalcDto.getCalcMethod(), Pair.of(CALC_TOTAL_SCORE, StringPool.EMPTY), ruleCalcDto.getRefList());
            if (result.score == null) {
                log.debug("LOG20713:");
                ruleCalcDto.setTotalScore(null);
                continue;
            }
            BigDecimal totalScore = getMaxScore(scoreSystem, result.score);
            log.debug("LOG20643:calcIndicatorTotalScore sdIndicatorId={}, originScore={}, totalScore={}, scoreSystem={}",
                ruleCalcDto.getSdIndicatorId(), result.score, totalScore, scoreSystem);
            ruleCalcDto.setTotalScore(totalScore);
        }
    }

    public void clearCalcResult(String orgId, String xpdId) {
        log.info("clearCalcResult orgId {} xpdId {}", orgId, xpdId);
        String lockKey = String.format(RedisKeys.LK_XPD_CALC_EXECUTE, xpdId);
        Pair<Integer, TimeUnit> lockTime = Pair.of(6, TimeUnit.MINUTES);
        boolean lockRun = CommonUtils.tryLockRun(lockKey, 0, lockTime.getKey(), lockTime.getValue(), () -> {
            int removeQty = 0;
            while ((removeQty = xpdResultUserIndicatorMapper.removeCalcByXpdId(orgId, xpdId)) > 0) {
                log.info("removeCalcUserIndicator xpdId {} removeQty {}", xpdId, removeQty);
            }
            while ((removeQty = xpdResultUserDimMapper.removeCalcByXpdId(orgId, xpdId)) > 0) {
                log.info("removeCalcUserDim xpdId {} removeQty {}", xpdId, removeQty);
            }
            while ((removeQty = xpdResultUserMapper.removeCalcByXpdId(orgId, xpdId)) > 0) {
                log.info("removeCalcUserResult xpdId {} removeQty {}", xpdId, removeQty);
            }
            xpdDimMapper.clearScoreByXpdId(orgId, xpdId);
        });
        if (!lockRun) {
            throw new ApiException(ExceptionKeys.XPD_CALC_RESULT_ING);
        }
    }

    public void calcResult(String orgId, String xpdId, boolean endProj) {
        ErrorInfo errorInfo = SpringContextHolder.getBean(XpdRuleConfAppService.class)
            .checkXpdRuleConf4Common(orgId, xpdId, false);
        if (errorInfo != null && errorInfo.getCode() != 0) {
            throw new ApiException(ExceptionKeys.XPD_RULE_CONF_CHECK_FAIL);
        }
        CalcResultRefContext refContext = initCalcRefContext(orgId, xpdId, true);
        if (refContext == null) {
            log.warn("calcResult initCalcRefContext is null, xpdId {}", xpdId);
            return;
        }
        String lockKey = String.format(RedisKeys.LK_XPD_CALC_EXECUTE, xpdId);
        if (redisRepo.getRedisRepository().hasKey(lockKey)) {
            throw new ApiException(ExceptionKeys.XPD_CALC_RESULT_ING);
        }
        //异步调用计算
        SpCallUtils.asyncCall(() -> {
            RequestContextHolder.resetRequestAttributes();
            doCalcResult(refContext, endProj);
        });
    }

    /**
     * 盘点迁移后，结果计算
     * @param orgId
     * @param xpdId
     */
    public String calcMigIndicatorResult(String orgId, String xpdId, int queryResult) {
        String cacheKey = String.format(RedisKeys.CACHE_CALC_MIG_INDICATOR_RESULT, xpdId);
        if (queryResult == YesOrNo.YES.getValue()) {
            return redisRepo.getValue(cacheKey);
        }
        //0:失败，1：成功，2：正在执行中，3:没有可处理的数据
        String result = "0";
        try {
            CalcResultRefContext refContext = initCalcRefContext(orgId, xpdId, true);
            if (refContext == null) {
                log.warn("calcResult initCalcRefContext is null, xpdId {}", xpdId);
                result = "3";
                return result;
            }
            //开始执行设置为进行中
            redisRepo.setValue(cacheKey, "2", Pair.of(1L, TimeUnit.HOURS));
            String lockKey = String.format(RedisKeys.LK_XPD_CALC_EXECUTE, xpdId);
            Pair<Integer, TimeUnit> lockTime = Pair.of(8, TimeUnit.MINUTES);
            boolean lockRun = CommonUtils.tryLockRun(lockKey, 0, lockTime.getKey(), lockTime.getValue(), () -> {
                boolean successEnd = false;
                try {
                    SpCallUtils.registerKeepalive(lockKey, () -> redisRepo.expire(lockKey, lockTime));
                    String aomProjId = refContext.aomPrjId;
                    XpdCalcBatchDTO calcBatch = refContext.calcBatch;
                    List<String> allUserIds = rvActivityParticipationMemberRepo.findAllUserIdByActId(orgId, aomProjId);
                    BatchOperationUtil.batchExecute(allUserIds, CALC_BATCH_COUNT, subUserIds -> {
                        log.info("{} calcMigIndicatorResult", refContext.logPrefix());
                        calcNotUsedIndicatorUserResult(refContext, subUserIds);
                    });
                    //删掉未计算到的用户结果
                    int removeQty = 0;
                    while ((removeQty = xpdResultUserIndicatorMapper.removeNotCalcByXpdId(orgId, xpdId, calcBatch.getBatchNo())) > 0) {
                        log.info("{} removeNotCalcUserIndicator removeQty {}", refContext.logPrefix(), removeQty);
                    }
                    calcNotUsedIndicatorTotalScore(refContext);
                    successEnd = true;
                } finally {
                    SpCallUtils.removeKeepalive(lockKey, null);
                    xpdCalcLogRepo.endCalcLog(refContext.calcBatch.getId(), successEnd);
                }
            });
            result = lockRun ? "1" : "2";
        } finally {
            //结果保存一小时
            redisRepo.setValue(cacheKey, result, Pair.of(1L, TimeUnit.HOURS));
        }
        return result;
    }

    /**
     * 计算结果
     *
     * @param refContext
     */
    private void doCalcResult(CalcResultRefContext refContext, boolean endProj) {
        String xpdId = refContext.xpdId;
        String lockKey = String.format(RedisKeys.LK_XPD_CALC_EXECUTE, xpdId);
        Pair<Integer, TimeUnit> lockTime = Pair.of(8, TimeUnit.MINUTES);
        boolean lockRun = CommonUtils.tryLockRun(lockKey, 0, lockTime.getKey(), lockTime.getValue(), () -> {
            boolean successEnd = false;
            try {
                SpCallUtils.registerKeepalive(lockKey, () -> redisRepo.expire(lockKey, lockTime));
                String orgId = refContext.orgId;
                String aomProjId = refContext.aomPrjId;
                List<DimRuleMetaDTO> dimRuleList = refContext.allXpdDimList;
                XpdCalcBatchDTO calcBatch = refContext.calcBatch;
                List<String> allUserIds = rvActivityParticipationMemberRepo.findAllUserIdByActId(orgId, aomProjId);
                BatchOperationUtil.batchExecute(allUserIds, CALC_BATCH_COUNT, subUserIds -> {
                    log.info("{} calcDimUserResult", refContext.logPrefix());
                    queryUserRefData(refContext, subUserIds);
                    calcDimUserResult(dimRuleList, refContext, subUserIds);
                    calcNotUsedIndicatorUserResult(refContext, subUserIds);
                    refContext.resetUserResult();
                });
                //删掉未计算到的用户结果
                int removeQty = 0;
                while ((removeQty = xpdResultUserIndicatorMapper.removeNotCalcByXpdId(orgId, xpdId, calcBatch.getBatchNo())) > 0) {
                    log.info("{} removeNotCalcUserIndicator removeQty {}", refContext.logPrefix(), removeQty);
                }
                while ((removeQty = xpdResultUserDimMapper.removeNotCalcByXpdId(orgId, xpdId, calcBatch.getBatchNo())) > 0) {
                    log.info("{} removeNotCalcUserDim removeQty {}", refContext.logPrefix(), removeQty);
                }
                calcResultIndicator(refContext);
                calcNotUsedIndicatorTotalScore(refContext);
                //计算按百分比设置分层等级的
                dimGridLevelCalcByPtg(refContext, dimRuleList, endProj);
                //计算纬度组合结果
                BatchOperationUtil.batchExecute(allUserIds, CALC_BATCH_COUNT, subUserIds -> calcDimCombUserResult(refContext, subUserIds));
                //清除维度计算用的指标关联map
                refContext.refIndicatorMap.clear();
                //项目结果计算
                XpdRulePO xpdRulePO = xpdRuleMapper.getByXpdId(refContext.orgId, refContext.xpdId);
                if (xpdRulePO == null) {
                    log.info("{} XpdRulePO is null", refContext.logPrefix());
                    //要不要清除老的计算结果
                    return;
                }
                buildXpdRefContext(refContext, xpdRulePO);
                prepareRefContext(refContext);
                BatchOperationUtil.batchExecute(allUserIds, CALC_BATCH_COUNT, subUserIds -> {
                    log.info("{} calcXpdResult", refContext.logPrefix());
                    queryUserRefData(refContext, subUserIds);
                    calcXpdResult(refContext, subUserIds);
                    refContext.resetUserResult();
                });
                //删掉未计算到的用户结果
                while ((removeQty = xpdResultUserMapper.removeNotCalcByXpdId(orgId, xpdId, calcBatch.getBatchNo())) > 0) {
                    log.info("removeNotCalcUserResult xpdId {} batchNo {} removeQty {}", xpdId, calcBatch.getBatchNo(), removeQty);
                }
                calcXpdLevelByPtg(refContext, endProj);
                //更新维度计算分数
                List<XpdDimPO> updateScoreList = BeanCopierUtil.convertList(refContext.allXpdDimList, dimMeta -> {
                    XpdDimPO dimPO = new XpdDimPO();
                    dimPO.setId(dimMeta.xpdDimId);
                    BigDecimal scoreTotal = Optional.ofNullable(dimMeta.dimRuleBase).map(XpdDimRuleDto::getCalcTotalScore).orElse(null);
                    if (scoreTotal != null) {
                        scoreTotal = ScoreSystemEnum.getMaxScore(refContext.scoreSystem, scoreTotal);
                    }
                    dimPO.setScoreTotal(scoreTotal);
                    return dimPO;
                });
                BatchOperationUtil.batchSave(updateScoreList, xpdDimMapper::batchUpdateScore);
                successEnd = true;
            } finally {
                SpCallUtils.removeKeepalive(lockKey, null);
                xpdCalcLogRepo.endCalcLog(refContext.calcBatch.getId(), successEnd);
            }
        });
        if (!lockRun) {
            throw new ApiException(ExceptionKeys.XPD_CALC_RESULT_ING);
        }
    }

    /**
     * 计算维度组结果
     * @param refContext
     * @param subUserIds
     */
    private void calcDimCombUserResult(CalcResultRefContext refContext, List<String> subUserIds) {
        // 删除现有的维度组结果
        xpdResultUserDimcombMapper.removeByXpdId(refContext.orgId, refContext.xpdId, subUserIds);
        // 维度组合列表
        if (CollectionUtils.isEmpty(refContext.dimCombList)) {
            log.warn("LOG20863:xpdId={}", refContext.xpdId);
            return;
        }

        // 获取用户在各维度的结果
        List<XpdResultUserDimPO> userDimResults =
            xpdResultUserDimMapper.findByXpdIdAndUserIds(refContext.orgId, refContext.xpdId, subUserIds);
        if (CollectionUtils.isEmpty(userDimResults)) {
            log.warn("LOG20873:xpdId={}", refContext.xpdId);
            return;
        }

        // 初始化宫格相关缓存
        if (refContext.xpdGrid == null || refContext.gridCoordinate == null) {
            log.warn("LOG20883:xpdId={}", refContext.xpdId);
            return;
        }

        // 按用户ID分组维度结果
        Map<String, List<XpdResultUserDimPO>> userDimResultMap = userDimResults.stream()
            .collect(Collectors.groupingBy(XpdResultUserDimPO::getUserId));

        List<XpdResultUserDimcombPO> resultList = new ArrayList<>();

        for (String userId : subUserIds) {
            for (XpdDimCombPO dimComb : refContext.dimCombList) {
                String xSdDimId = dimComb.getXSdDimId();
                String ySdDimId = dimComb.getYSdDimId();

                List<XpdResultUserDimPO> userResults = userDimResultMap.get(userId);
                if (CollectionUtils.isEmpty(userResults)) {
                    log.warn("LOG21023:xpdId={}, userId={}, dimCombId={}", refContext.xpdId, userId, dimComb.getId());
                    continue;
                }

                // 查找用户在X轴和Y轴维度上的结果
                XpdResultUserDimPO xDimResult = userResults.stream()
                    .filter(r -> Objects.equals(r.getSdDimId(), xSdDimId))
                    .findFirst().orElse(null);
                XpdResultUserDimPO yDimResult = userResults.stream()
                    .filter(r -> Objects.equals(r.getSdDimId(), ySdDimId))
                    .findFirst().orElse(null);

                if (xDimResult == null || yDimResult == null) {
                    log.warn("LOG21003:xpdId={}, userId={}, dimCombId={}", refContext.xpdId, userId, dimComb.getId());
                    continue;
                }

                // 根据X轴和Y轴的层级确定格子位置
                String xLevelId = xDimResult.getGridLevelId();
                String yLevelId = yDimResult.getGridLevelId();

                if (StringUtils.isEmpty(xLevelId) || StringUtils.isEmpty(yLevelId)) {
                    log.warn("LOG20983:xpdId={}, userId={}, dimCombId={}", refContext.xpdId, userId, dimComb.getId());
                    continue;
                }

                // 获取X和Y坐标
                Integer xIndex = refContext.gridLevelOrderMap.get(xLevelId);
                Integer yIndex = refContext.gridLevelOrderMap.get(yLevelId);

                if (xIndex == null || yIndex == null) {
                    log.warn(
                        "LOG20893:xpdId={}, userId={}, dimCombId={}, xLevelId={}, yLevelId={}", refContext.xpdId,
                        userId, dimComb.getId(), xLevelId, yLevelId);
                    continue;
                }

                XpdGridCellPO gridCell = refContext.getGridCell(dimComb.getId(), xIndex, yIndex);
                if (gridCell == null) {
                    log.warn("LOG20823:xpdId={}", refContext.xpdId);
                    continue;
                }

                // 创建维度组结果记录
                XpdResultUserDimcombPO resultPO = new XpdResultUserDimcombPO();
                resultPO.setOrgId(refContext.orgId);
                resultPO.setXpdId(refContext.xpdId);
                resultPO.setUserId(userId);
                resultPO.setDimCombId(dimComb.getId());
                resultPO.setCellId(gridCell.getId());
                resultPO.setCellIndex(gridCell.getCellIndex());
                resultPO.setCalcBatchNo(refContext.calcBatch.getBatchNo());
                resultPO.setCaliFlag(YesOrNo.NO.getValue()); // 默认未校准
                EntityUtil.setAuditFields(resultPO);

                resultList.add(resultPO);
            }
        }

        // 批量保存结果
        if (!resultList.isEmpty()) {
            xpdResultUserDimcombMapper.batchInsert(resultList);
            log.info("LOG20008:saved={} results for xpdId={}", resultList.size(), refContext.xpdId);
        }
    }

    /**
     * 仅当按维度分层结果规则计算时才执行相应逻辑
     * @param orgId
     * @param xpdId
     */
    public void calcXpdResultOnlyByDim(String orgId, String xpdId) {
        XpdPO xpdPO = xpdMapper.selectByIdAndOrg(orgId, xpdId);
        if (xpdPO == null) {
            log.warn("calcXpdResultOnlyByDim skipped xpdId {}", xpdId);
            return;
        }
        XpdRulePO xpdRulePO = xpdRuleMapper.getByXpdId(orgId, xpdId);
        if (xpdRulePO == null) {
            log.info("XpdRulePO is null xpdId {}", xpdId);
            return;
        }
        if (!(Objects.equals(XpdCalcTypeEnum.BY_DIMENSION.getCode(), xpdRulePO.getCalcType())
              && Objects.equals(XpdResultTypeEnum.DIM_LEVEL_RESULT.getCode(), xpdRulePO.getResultType()))) {
            log.info("XpdRulePO not byDimension levelResult xpdId {}", xpdId);
            return;
        }
        //只按维度分层结果规则计算时执行
        String lockKey = String.format(RedisKeys.LK_XPD_CALC_EXECUTE, xpdId);
        Pair<Integer, TimeUnit> lockTime = Pair.of(6, TimeUnit.MINUTES);
        boolean lockRun = CommonUtils.tryLockRun(lockKey, 0, lockTime.getKey(), lockTime.getValue(), () -> {
            int removeQty = 0;
            XpdCalcBatchDTO calcBatch = xpdCalcLogRepo.newCalcBatch(orgId, CalcLogTypeEnum.XPD.getCode(), xpdPO.getId());
            CalcResultRefContext refContext = new CalcResultRefContext();
            refContext.orgId = orgId;
            refContext.xpdId = xpdPO.getId();
            refContext.aomPrjId = xpdPO.getAomPrjId();
            refContext.modelId = xpdPO.getModelId();
            refContext.calcBatch = calcBatch;
            buildXpdRefContext(refContext, xpdRulePO);
            List<String> allUserIds = rvActivityParticipationMemberRepo.findAllUserIdByActId(orgId, refContext.aomPrjId);
            BatchOperationUtil.batchExecute(allUserIds, CALC_BATCH_COUNT, subUserIds -> {
                log.info("{} calcXpdResult", refContext.logPrefix());
                calcXpdResult(refContext, subUserIds);
                refContext.resetUserResult();
            });
            //删掉未计算到的用户结果
            while ((removeQty = xpdResultUserMapper.removeNotCalcByXpdId(orgId, xpdId, calcBatch.getBatchNo())) > 0) {
                log.info("removeNotCalcUserResult xpdId {} batchNo {} removeQty {}", xpdId, calcBatch.getBatchNo(), removeQty);
            }
        });
        if (!lockRun) {
            throw new ApiException(ExceptionKeys.XPD_CALC_RESULT_ING);
        }
    }

    private CalcResultRefContext initCalcRefContext(String orgId, String xpdId, boolean calcResult) {
        XpdPO xpdPO = xpdMapper.selectByIdAndOrg(orgId, xpdId);
        XpdRuleConfPO xpdRuleConfPO = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
        if (xpdPO == null || xpdRuleConfPO == null) {
            log.warn("initCalcRefContext skipped xpdId {} xpdPO {} xpdRuleConfPO {}",
                xpdId, xpdPO, xpdRuleConfPO);
            return null;
        }
        XpdCalcBatchDTO calcBatch = null;
        if (calcResult) {
            calcBatch = xpdCalcLogRepo.newCalcBatch(orgId, CalcLogTypeEnum.XPD.getCode(), xpdPO.getId());
        }
        List<XpdImportPO> importPOList = xpdImportMapper.selectByXpdId(orgId, xpdId, null);
        List<DimRuleMetaDTO> allDimRuleList = buildRuleList(xpdPO, importPOList);
        CalcResultRefContext refContext = new CalcResultRefContext();
        refContext.orgId = orgId;
        refContext.xpdId = xpdPO.getId();
        refContext.aomPrjId = xpdPO.getAomPrjId();
        refContext.modelId = xpdPO.getModelId();
        refContext.resultType = xpdRuleConfPO.getResultType();
        refContext.calcBatch = calcBatch;
        refContext.allXpdDimList = allDimRuleList;
        refContext.scoreSystem = Optional.ofNullable(xpdRuleConfPO.getScoreSystem()).orElse(ScoreSystemEnum.ORIGINAL.getCode());
        refContext.gridId = xpdRuleConfPO.getGridId();
        refContext.xpdImportMap = StreamUtil.list2map(importPOList, XpdImportPO::getId);
        Map<String, AomActvExtBO> aomActvMap = StreamUtil.list2map(
            xpdAomService.activityIndicatorList(refContext.orgId,refContext.aomPrjId),
            AomActvExtBO::getActvRefId);
        refContext.aomActvMap = aomActvMap;
        refContext.dimIndicatorsMap = new HashMap<>();
        spsdAclService.dimIndicatorMapByDims(orgId, refContext.modelId,
                BeanCopierUtil.convertList(allDimRuleList, item -> item.sdDimId))
            .forEach((dimBase, indicators) -> refContext.dimIndicatorsMap.put(dimBase.getDmId(), Pair.of(dimBase, indicators)));
        List<IndicatorDto> indicators = spsdAclService.getLastIndicators(orgId, xpdPO.getModelId());
        refContext.modelIndicatorsMap = StreamUtil.list2map(indicators, IndicatorDto::getItemId);
        buildRefContext(refContext, refContext.allXpdDimList);
        prepareRefContext(refContext);
        calcDimRuleTotalScore(refContext);
        refContext.initGridCache();
        return refContext;
    }

    private void calcDimRuleTotalScore(CalcResultRefContext refContext) {
        log.info("{} calcDimRuleTotalScore start", refContext.logPrefix());
        for (DimRuleMetaDTO dimRuleMetaDTO : refContext.allXpdDimList) {
            if (!dimRuleMetaDTO.coverByImport && dimRuleMetaDTO.dimRuleBase != null) {
                if (DimCalcTypeEnum.isPerf(dimRuleMetaDTO.dimRuleBase.getCalcType())) {
                    BigDecimal totalScore = Optional.ofNullable(refContext.queryIndicatorInfo(DimRuleCalcRefEnum.AOM_ACT.getCode(),
                            dimRuleMetaDTO.dimRuleBase.getAomActId(), dimRuleMetaDTO.dimRuleBase.getSdIndicatorId()))
                        .map(RefIndicatorInfo::getTotalScore)
                        .orElse(BigDecimal.ZERO);
                    //calc-checked
                    dimRuleMetaDTO.dimRuleBase.setCalcTotalScore(totalScore);
                } else if (Objects.equals(dimRuleMetaDTO.dimRuleBase.getCalcType(), DimCalcTypeEnum.INDICATOR.getCode())) {
                    if (Objects.equals(DimCalcRuleEnum.FORMULA.getCode(), dimRuleMetaDTO.dimRuleBase.getCalcRule())) {
                        BigDecimal totalScore = formulaResult(refContext, dimRuleMetaDTO.dimRuleBase.getFormulaCalc(), Pair.of(CALC_TOTAL_SCORE, null));
                        //可能为null,计算公式有被除数是0
                        dimRuleMetaDTO.dimRuleBase.setCalcTotalScore(totalScore);
                    } else {
                        DimResult dimResult = calcDimByIndicatorResult(refContext, dimRuleMetaDTO.ruleCalcList,
                            new CalcDimResultParam(false, refContext.calcDimByScore()), Pair.of(CALC_TOTAL_SCORE, StringPool.EMPTY));
                        dimRuleMetaDTO.dimRuleBase.setCalcTotalScore(dimResult.getScore());
                        calcDimByIndicatorResult(refContext, dimRuleMetaDTO.ruleCalcList,
                            new CalcDimResultParam(false, refContext.calcDimByScore()), Pair.of(CALC_STANDARD_SCORE, StringPool.EMPTY));
                    }
                } else {
                    //子维度结果
                    boolean hasSubDim = CollectionUtils.isNotEmpty(dimRuleMetaDTO.subDimRules);
                    boolean hasRuleList = CollectionUtils.isNotEmpty(dimRuleMetaDTO.ruleCalcList);
                    DimResult dimResult = null;
                    if (hasSubDim) {
                        dimResult = calcSubDimensionResult(dimRuleMetaDTO.subDimRules, refContext, Pair.of(CALC_TOTAL_SCORE, StringPool.EMPTY));
                        calcSubDimensionResult(dimRuleMetaDTO.subDimRules, refContext, Pair.of(CALC_STANDARD_SCORE, StringPool.EMPTY));
                    } else if (hasRuleList) {
                        dimResult = calcDimByIndicatorResult(refContext, dimRuleMetaDTO.ruleCalcList,
                            new CalcDimResultParam(false, refContext.calcDimByScore()), Pair.of(CALC_TOTAL_SCORE, StringPool.EMPTY));
                        calcDimByIndicatorResult(refContext, dimRuleMetaDTO.ruleCalcList,
                            new CalcDimResultParam(false, refContext.calcDimByScore()), Pair.of(CALC_STANDARD_SCORE, StringPool.EMPTY));

                    }
                    dimRuleMetaDTO.dimRuleBase.setCalcTotalScore(nullAsZero(dimResult, DimResult::getScore));
                }
            }
        }
        log.info("{} calcDimRuleTotalScore end", refContext.logPrefix());
    }

    private void calcXpdLevelByPtg(CalcResultRefContext refContext, boolean endProj) {
        log.info("{} calcXpdLevelByPtg", refContext.logPrefix());
        Integer resultType = refContext.xpdRuleDto.getResultType();
        if (XpdLevelTypeEnum.needAfterResult(refContext.xpdRuleDto.getLevelType())
            && !Objects.equals(resultType, XpdResultTypeEnum.DIM_LEVEL_RESULT.getCode())) {
            log.info("{} calcXpdLevelByPtg condition match", refContext.logPrefix());
            //按比例&不是分层结果的
            boolean getScore = Objects.equals(XpdResultTypeEnum.SCORE.getCode(), resultType);
            List<DecimalPtgBean> ptgBeans = xpdResultUserMapper.listSortValue(refContext.orgId,
                refContext.xpdId, getScore);
            RatioLevelThresholdDto thresholdDto = new RatioLevelThresholdDto();
            thresholdDto.setInvalidScore(YesOrNo.YES.getValue());
            try {
                List<XpdRuleLevelDto> xpdRuleLevels = refContext.xpdRuleLevels;
                if (XpdLevelTypeEnum.byRatio(refContext.xpdRuleDto.getLevelType())) {
                    if (ptgBeans.isEmpty()) {
                        return;
                    }
                    //xpdRuleLevels 分层顺序从高到低
                    if (DimLevelPriorityEnum.LOW.getCode().equals(refContext.xpdRuleDto.getLevelPriority())) {
                        //从低到高排序
                        IArrayUtils.sortList(ptgBeans, DecimalPtgBean::getSortValue);
                        //分层顺序反转为从低到高
                        xpdRuleLevels = Optional.ofNullable(xpdRuleLevels).orElse(Lists.newArrayList());
                        Collections.reverse(xpdRuleLevels);
                    } else {
                        //从高到低排序
                        IArrayUtils.sortListDesc(ptgBeans, DecimalPtgBean::getSortValue);
                    }
                    thresholdDto = calcLevelMatchByPtg(ptgBeans, xpdRuleLevels, XpdRuleLevelDto::getLevelValue, XpdRuleLevelDto::getId, (ptgBean, level) -> {
                        ptgBean.setCompetent(Optional.ofNullable(level.getCompetent()).orElse(YesOrNo.NO.getValue()));
                    });
                } else {
                    //综合判断
                    //综合判断没有无效的情况
                    thresholdDto.setInvalidScore(YesOrNo.NO.getValue());
                    Collection<Pair<String, BigDecimal>> ptgValues = spRuleService.ptgValues(Lists.newArrayList(RvRuleTypeEnum.XPD_JUDGE_RULE_RATIO.columnType()),
                        xpdRuleLevels, XpdRuleLevelDto::getRuleConfig);
                    thresholdDto.setLevelScore(calcPtgScoreMap(ptgValues, ptgBeans));
                    RuleMainBase mainData = new RuleMainBase();
                    mainData.setOrgId(refContext.orgId);
                    mainData.setBizId(refContext.xpdId);
                    mainData.setBizData(thresholdDto.getLevelScore());
                    mainData.setColValFunc(param -> BeanCopierUtil.convertList(param.getInputList(), ptgBeanObj -> {
                        DecimalPtgBean ptgBean = (DecimalPtgBean) ptgBeanObj;
                        RuleColumnValueBean valueBean = new RuleColumnValueBean();
                        valueBean.setColumnId(IArrayUtils.getFirst(param.getColumnIds()));
                        valueBean.setObjectId(ptgBean.getId());
                        valueBean.setValue(ptgBean.getSortValue());
                        return valueBean;
                    }));
                    spRuleService.calcFirstMatch(mainData, ptgBeans, xpdRuleLevels, DecimalPtgBean::getId, XpdRuleLevelDto::getRuleConfig, (ptgBean, level) -> {
                        ptgBean.setLevelId(level.getId());
                        ptgBean.setCompetent(Optional.ofNullable(level.getCompetent()).orElse(YesOrNo.NO.getValue()));
                        ptgBean.setAllocated(true);
                    });
                }
                //批量更新分层id
                BatchOperationUtil.batchSave(ptgBeans, xpdResultUserMapper::batchUpdateLevel);
            } finally {
                if (endProj) {
                    xpdRuleMapper.updateRuleThreshold(refContext.xpdRuleDto.getId(), JSON.toJSONString(thresholdDto), thresholdDto.getInvalidScore());
                }
            }
        }
    }

    private void calcXpdResult(CalcResultRefContext refContext, List<String> userIds) {
        XpdRuleMainDto xpdRuleDto = refContext.xpdRuleDto;
        List<XpdResultUserPO> resultList = null;
        if (Objects.equals(XpdCalcTypeEnum.BY_INDICATOR.getCode(), xpdRuleDto.getCalcType())) {
            boolean byScore = Objects.equals(xpdRuleDto.getResultType(), XpdResultTypeEnum.SCORE.getCode());
            if (byScore && Objects.equals(xpdRuleDto.getCalcRule(), XpdCalcRuleEnum.FORMULA.getCode())) {
                resultList = calcXpdFormulaResult(refContext, xpdRuleDto.getFormulaCalc(), userIds);
            } else {
                resultList = calcXpdUserResultByIndicator(refContext, byScore, userIds);
            }
        } else if (Objects.equals(XpdResultTypeEnum.SCORE.getCode(), xpdRuleDto.getResultType())) {
            resultList = calcXpdUserResultByDimScore(refContext, userIds);
        } else if (Objects.equals(XpdResultTypeEnum.RATIO.getCode(), xpdRuleDto.getResultType())) {
            resultList = calcXpdUserResultByDimPtg(refContext, userIds);
        } else if (Objects.equals(XpdResultTypeEnum.DIM_LEVEL_RESULT.getCode(), xpdRuleDto.getResultType())) {
            RuleMainBase mainData = new RuleMainBase();
            mainData.setOrgId(refContext.orgId);
            mainData.setBizId(refContext.xpdId);
            List<XpdResultUserPO> finalResultList = new ArrayList<>();
            spRuleService.calcFirstMatch(mainData, userIds, refContext.xpdRuleLevels,
                u -> u, XpdRuleLevelDto::getRuleConfig, (userId, ruleLevelDto) -> {
                    XpdResultUserPO resultUserPO = refContext.newUserResult(userId, null, null);
                    resultUserPO.setXpdLevelId(ruleLevelDto.getId());
                    resultUserPO.setCompetent(Optional.ofNullable(ruleLevelDto.getCompetent())
                        .orElse(YesOrNo.NO.getValue()));
                    finalResultList.add(resultUserPO);
                });
            resultList = finalResultList;
        }
        //保存结果
        saveUserResult(refContext, resultList);
    }

    private void saveUserResult(CalcResultRefContext refContext, List<XpdResultUserPO> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }
        List<UserResultIdDTO> existUserIds = xpdResultUserMapper.queryIgnoreDelByXpdId(refContext.orgId, refContext.xpdId,
            BeanCopierUtil.convertList(resultList, XpdResultUserPO::getUserId));
        Map<String, String> userId2PkMap = IArrayUtils.listAsMap(existUserIds, UserResultIdDTO::getUserId, UserResultIdDTO::getId);
        List<XpdResultUserPO> updateList = new ArrayList<>();
        List<XpdResultUserPO> insertList = new ArrayList<>();
        resultList.forEach(userResult -> {
            String existId = userId2PkMap.get(userResult.getUserId());
            if (StringUtils.isEmpty(existId)) {
                insertList.add(userResult);
            } else {
                userResult.setId(existId);
                updateList.add(userResult);
            }
        });
        BatchOperationUtil.batchSave(insertList, xpdResultUserMapper::insertBatchSomeColumn);
        BatchOperationUtil.batchSave(updateList, xpdResultUserMapper::batchUpdateResult);
    }

    private List<XpdResultUserPO> calcXpdFormulaResult(CalcResultRefContext refContext, ExpressionCalc formulaCalc, List<String> userIds) {
        if (formulaCalc == null) {
            return null;
        }
        List<XpdResultUserPO> resultList = new ArrayList<>();
        BigDecimal scoreTotal = formulaResult(refContext, formulaCalc, Pair.of(CALC_TOTAL_SCORE, null));
        for (String userId : userIds) {
            BigDecimal scoreVal = formulaResult(refContext, formulaCalc, Pair.of(CALC_USER, userId));
            if (scoreVal != null) {
                if (scoreTotal != null) {
                    scoreVal = convertToTargetSystem(scoreVal, scoreTotal, refContext.scoreSystem);
                } else {
                    log.warn("LOG20743:");
                }
                addUserResultIfNeed(refContext, resultList,
                    refContext.newUserResult(userId, scoreVal, null), true);
            }
        }
        return resultList;
    }

    private List<XpdResultUserPO> calcXpdUserResultByIndicator(CalcResultRefContext refContext, boolean byScore, List<String> userIds) {
        List<XpdResultUserPO> resultList = new ArrayList<>();
        for (String userId : userIds) {
            DimResult dimResult = calcDimByIndicatorResult(refContext, refContext.xpdCalcIndRules,
                new CalcDimResultParam(false, byScore), Pair.of(CALC_USER, userId));

            if (!dimResult.hasNoResult) {
                addUserResultIfNeed(refContext, resultList,
                    refContext.newUserResult(userId, dimResult.score, dimResult.qualifiedPtg), byScore);
            }
        }
        return resultList;
    }

    private void addUserResultIfNeed(CalcResultRefContext refContext, List<XpdResultUserPO> resultList, XpdResultUserPO userResult, boolean byScore) {
        //计算分层等级
        XpdRuleLevelDto matchLevel = minMatchLevelRule(refContext.xpdRuleLevels,
            XpdRuleLevelDto::getLevelValue, byScore ? userResult.getScoreValue() : userResult.getQualifiedPtg());
        if (matchLevel != null || XpdLevelTypeEnum.needAfterResult(refContext.xpdRuleDto.getLevelType())) {
            userResult.setXpdLevelId(Optional.ofNullable(matchLevel).map(XpdRuleLevelDto::getId).orElse(StringPool.EMPTY));
            userResult.setCompetent(Optional.ofNullable(matchLevel).map(XpdRuleLevelDto::getCompetent).orElse(YesOrNo.NO.getValue()));
            resultList.add(userResult);
        }
    }

    private List<XpdResultUserPO> calcXpdUserResultByDimScore(CalcResultRefContext refContext, List<String> userIds) {
        List<XpdResultUserPO> resultList = new ArrayList<>();
        List<XpdUserDimResultDTO> dimResultList = xpdResultUserDimMapper.queryResultByDimUserIds(
            refContext.orgId, refContext.xpdId,
            BeanCopierUtil.convertList(refContext.xpdCalcDimRules, XpdRuleCalcDimPO::getSdDimId), userIds);
        Map<String, Map<String, XpdUserDimResultDTO>> sdDimUserRetMap = new HashMap<>();
        refContext.xpdCalcDimRules.forEach(calcDim -> sdDimUserRetMap.put(calcDim.getSdDimId(), new HashMap<>()));
        dimResultList.forEach(dimResult -> sdDimUserRetMap.get(dimResult.getSdDimId()).put(
            dimResult.getUserId(), dimResult));
        //按维度得分计算
        for (String userId : userIds) {
            boolean hasNoResult = false;
            BigDecimal totalValue = BigDecimal.ZERO;
            for (XpdRuleCalcDimPO xpdCalcDimRule : refContext.xpdCalcDimRules) {
                XpdUserDimResultDTO dimResult = sdDimUserRetMap.get(xpdCalcDimRule.getSdDimId()).get(userId);
                if (dimResult == null) {
                    log.info("LOG20753:userId={}, sdDimId={} has no dim result", userId, xpdCalcDimRule.getSdDimId());
                    hasNoResult = true;
                    continue;
                }
                // 由于维度得分表中的得分已经过分制转换，所以这里不能再进行二次转换
                totalValue = totalValue.add(multiplyPtg(dimResult.getScoreValue(), xpdCalcDimRule.getWeight()));
            }
            if (!hasNoResult) {
                //有结果保存
                addUserResultIfNeed(refContext, resultList,
                    refContext.newUserResult(userId, totalValue, null),
                    true);
            }
        }
        return resultList;
    }

    private List<XpdResultUserPO> calcXpdUserResultByDimPtg(CalcResultRefContext refContext, List<String> userIds) {
        List<DimRuleMetaDTO> xpdDimList = Optional.ofNullable(refContext.allXpdDimList).orElse(Collections.emptyList());
        xpdDimList = xpdDimList.stream().filter(item -> !item.coverByImport).collect(Collectors.toList());
        if (xpdDimList.isEmpty()) {
            return null;
        }
        List<XpdResultUserPO> resultList = new ArrayList<>();
        List<XpdUserDimResultDTO> dimResultList = xpdResultUserDimMapper.queryResultByDimUserIds(
            refContext.orgId, refContext.xpdId,
            BeanCopierUtil.convertList(xpdDimList, item -> item.sdDimId), userIds);
        Map<String, Map<String, XpdUserDimResultDTO>> sdDimUserRetMap = new HashMap<>();
        xpdDimList.forEach(item -> sdDimUserRetMap.put(item.sdDimId, new HashMap<>()));
        dimResultList.forEach(dimResult -> sdDimUserRetMap.get(dimResult.getSdDimId()).put(
            dimResult.getUserId(), dimResult));
        for (String userId : userIds) {
            boolean hasNoResult = false;
            BigDecimal totalValue = BigDecimal.ZERO;
            for (DimRuleMetaDTO dimRule : xpdDimList) {
                String sdDimId = dimRule.sdDimId;
                XpdUserDimResultDTO dimResult = sdDimUserRetMap.get(sdDimId).get(userId);
                if (dimResult == null) {
                    hasNoResult = true;
                    continue;
                }
                totalValue = totalValue.add(nullAsZero(dimResult.getQualifiedPtg()));
            }
            if (!hasNoResult) {
                //有结果保存
                addUserResultIfNeed(refContext, resultList,
                    refContext.newUserResult(userId, null,
                        totalValue.divide(BigDecimal.valueOf(xpdDimList.size()), MC_TWO.getPrecision(), MC_TWO.getRoundingMode())),
                    false);
            }
        }
        return resultList;
    }

    private void calcResultIndicator(CalcResultRefContext refContext) {
        log.info("{} calcResultIndicator", refContext.logPrefix());
        Map<String, XpdIndicatorStaDTO> indStaMap = StreamUtil.list2map(
            xpdResultUserIndicatorMapper.queryIndicatorSta(refContext.orgId, refContext.xpdId),
            XpdIndicatorStaDTO::getSdIndicatorId);
        List<XpdResultIndicatorPO> resultIndList = new ArrayList<>();
        refContext.indicatorRuleCfgMap.forEach((sdIndicatorId, ruleCfg) -> {
            XpdResultIndicatorPO indicatorPO = new XpdResultIndicatorPO();
            indicatorPO.initData(null);
            indicatorPO.setOrgId(refContext.orgId);
            indicatorPO.setXpdId(refContext.xpdId);
            indicatorPO.setSdIndicatorId(sdIndicatorId);
            BigDecimal originalScore = nullAsZero(ruleCfg.getCalcTotalScore());
            indicatorPO.setScoreTotal(getMaxScore(refContext.scoreSystem, originalScore));
            indicatorPO.setScoreStandard(convertToTargetSystem(ruleCfg.getCalcStandardScore(), originalScore, refContext.scoreSystem));
            XpdIndicatorStaDTO indSta = indStaMap.getOrDefault(sdIndicatorId, new XpdIndicatorStaDTO());
            if (indSta.getTotalQty() == 0) {
                indicatorPO.setScoreAvg(BigDecimal.ZERO);
                indicatorPO.setQualifiedPtg(BigDecimal.ZERO);
            } else {
                indicatorPO.setScoreAvg(nullAsZero(indSta.getScoreValue())
                    .divide(BigDecimal.valueOf(indSta.getTotalQty()), MC_TWO.getPrecision(), MC_TWO.getRoundingMode()));
                indicatorPO.setQualifiedPtg(BigDecimal.valueOf(indSta.getQualifiedQty() * 100L)
                    .divide(BigDecimal.valueOf(indSta.getTotalQty()), MC_TWO.getPrecision(), MC_TWO.getRoundingMode()));
            }
            resultIndList.add(indicatorPO);
        });
        xpdResultIndicatorMapper.removeByXpdId(refContext.orgId, refContext.xpdId);
        BatchOperationUtil.batchSave(resultIndList, xpdResultIndicatorMapper::insertBatchSomeColumn);
    }

    private void dimGridLevelCalcByPtg(CalcResultRefContext refContext, List<DimRuleMetaDTO> dimRuleList, boolean endProj) {
        log.info("{} dimGridLevelCalcByPtg", refContext.logPrefix());
        boolean getScore = refContext.resultType == DimResultTypeEnum.SCORE_VALUE.getCode();
        for (DimRuleMetaDTO dimRuleMeta : dimRuleList) {
            Integer levelType = Optional.ofNullable(dimRuleMeta.dimRuleBase)
                .map(XpdDimRuleDto::getLevelType).orElse(null);
            //导入的结果，不是按比例或者综合判断的，按绩效等级计算的
            if (dimRuleMeta.coverByImport || !DimLevelTypeEnum.needAfterResult(levelType)
                || Objects.equals(dimRuleMeta.dimRuleBase.getCalcType(), DimCalcTypeEnum.PERF_RESULT.getCode())) {
                continue;
            }
            log.info("{} dimGridLevelCalcByPtg sdDimId {} coverByImport {}", refContext.logPrefix(),
                dimRuleMeta.sdDimId, dimRuleMeta.coverByImport);
            RatioLevelThresholdDto thresholdDto = new RatioLevelThresholdDto();
            thresholdDto.setInvalidScore(YesOrNo.YES.getValue());
            try {
                List<DimGridLevelRuleDTO> gridLevelRules = dimRuleMeta.dimRuleBase.getGridLevelRules();
                if (CollectionUtils.isEmpty(gridLevelRules)) {
                    log.info("dimGridLevelCalcByPtg emptyRules batchNo {} orgId {} xpdId {} sdDimId {}",
                        refContext.calcBatch.getBatchNo(), refContext.orgId, refContext.xpdId, dimRuleMeta.sdDimId);
                    continue;
                }
                List<DecimalPtgBean> ptgBeans = xpdResultUserDimMapper.listSortValue(refContext.orgId,
                    refContext.xpdId, dimRuleMeta.sdDimId,
                    //全局得分配置或者绩效得分
                    getScore || Objects.equals(dimRuleMeta.dimRuleBase.getCalcType(), DimCalcTypeEnum.PERF_SCORE.getCode()));
                if (DimLevelTypeEnum.byRatio(levelType)) {
                    if (ptgBeans.isEmpty()) {
                        continue;
                    }
                    //按比例计算
                    //gridLevelRules 分层顺序从高到低
                    if (DimLevelPriorityEnum.LOW.getCode().equals(dimRuleMeta.dimRuleBase.getLevelPriority())) {
                        //从低到高排序
                        IArrayUtils.sortList(ptgBeans, DecimalPtgBean::getSortValue);
                        //分层顺序反转为从低到高
                        gridLevelRules = gridLevelRules.stream().collect(Collectors.toList());
                        Collections.reverse(gridLevelRules);
                    } else {
                        //从高到低排序
                        IArrayUtils.sortListDesc(ptgBeans, DecimalPtgBean::getSortValue);
                    }
                    thresholdDto = calcLevelMatchByPtg(ptgBeans, gridLevelRules, DimGridLevelRuleDTO::getBaseValue, DimGridLevelRuleDTO::getGridLevelId);
                    //批量更新分层id
                    BatchOperationUtil.batchSave(ptgBeans, xpdResultUserDimMapper::batchUpdateLevelId);
                } else {
                    //综合判断的
                    //综合判断没有无效分数的情况
                    thresholdDto.setInvalidScore(YesOrNo.NO.getValue());
                    Collection<Pair<String, BigDecimal>> ptgValues = spRuleService.ptgValues(Lists.newArrayList(RvRuleTypeEnum.XPD_JUDGE_RULE_RATIO.columnType()),
                        gridLevelRules, DimGridLevelRuleDTO::getJudgeRule);
                    thresholdDto.setLevelScore(calcPtgScoreMap(ptgValues, ptgBeans));
                    RuleMainBase mainData = new RuleMainBase();
                    mainData.setOrgId(refContext.orgId);
                    mainData.setBizId(refContext.xpdId);
                    mainData.setBizData(thresholdDto.getLevelScore());
                    mainData.setColValFunc(param -> BeanCopierUtil.convertList(param.getInputList(), ptgBeanObj -> {
                        DecimalPtgBean ptgBean = (DecimalPtgBean) ptgBeanObj;
                        RuleColumnValueBean valueBean = new RuleColumnValueBean();
                        valueBean.setColumnId(IArrayUtils.getFirst(param.getColumnIds()));
                        valueBean.setObjectId(ptgBean.getId());
                        valueBean.setValue(ptgBean.getSortValue());
                        return valueBean;
                    }));
                    spRuleService.calcFirstMatch(mainData, ptgBeans, gridLevelRules, DecimalPtgBean::getId, DimGridLevelRuleDTO::getJudgeRule, (ptgBean, levelRule) -> {
                        ptgBean.setLevelId(levelRule.getGridLevelId());
                        ptgBean.setAllocated(true);
                    });
                    //批量更新分层id
                    BatchOperationUtil.batchSave(ptgBeans, xpdResultUserDimMapper::batchUpdateLevelId);
                }
            } finally {
                if (endProj) {
                    xpdDimRuleMapper.updateRuleThreshold(dimRuleMeta.dimRuleBase.getId(), JSON.toJSONString(thresholdDto), thresholdDto.getInvalidScore());
                }
            }
        }
    }

    private Map<String, BigDecimal> calcPtgScoreMap(Collection<Pair<String, BigDecimal>> ptgValues, List<DecimalPtgBean> ptgBeans) {
        Map<String, BigDecimal> scoreMap = new HashMap<>();
        if (!ptgValues.isEmpty()) {
            //从低到高排序
            IArrayUtils.sortList(ptgBeans, DecimalPtgBean::getSortValue);
            BigDecimal sizeDecimal = BigDecimal.valueOf(ptgBeans.size());
            for (Pair<String, BigDecimal> pair : ptgValues) {
                String ptgValKey = pair.getKey();
                int index = pair.getValue().multiply(sizeDecimal).divide(AppConstants.HUNDRED, 0, RoundingMode.HALF_DOWN).intValue() - 1;
                if (index < 0) {
                    index = 0;
                }
                index = Math.min(index, ptgBeans.size() - 1);
                scoreMap.put(ptgValKey, ptgBeans.get(index).getSortValue());
            }
        }
        return scoreMap;
    }

    private <T> RatioLevelThresholdDto calcLevelMatchByPtg(
        List<DecimalPtgBean> ptgBeans,
        List<T> levelRules,
        Function<T, BigDecimal> ptgGetter, Function<T, String> levelIdGetter) {
        return calcLevelMatchByPtg(ptgBeans, levelRules, ptgGetter, levelIdGetter, (ptgBean, level) -> {});
    }
    private <T> RatioLevelThresholdDto calcLevelMatchByPtg(
        List<DecimalPtgBean> ptgBeans,
        List<T> levelRules,
        Function<T, BigDecimal> ptgGetter, Function<T, String> levelIdGetter,
        BiConsumer<DecimalPtgBean, T> levelConsumer) {
        RatioLevelThresholdDto thresholdDto = new RatioLevelThresholdDto();
        if (CollectionUtils.isEmpty(ptgBeans) || CollectionUtils.isEmpty(levelRules)) {
            thresholdDto.setInvalidScore(YesOrNo.YES.getValue());
            return thresholdDto;
        }
        T defaultLevel = levelRules.get(levelRules.size() - 1);
        String defaultLevelId = levelIdGetter.apply(defaultLevel);
        int maxIndex = 0;
        BigDecimal upPtgValue = BigDecimal.ZERO;
        BigDecimal userQty = BigDecimal.valueOf(ptgBeans.size());
        Map<String, BigDecimal> levelScore = new HashMap<>();
        boolean validScore = true;
        String beforeLevelId = null;
        for (T levelRule : levelRules) {
            BigDecimal baseValue = ptgGetter.apply(levelRule);
            String levelId = levelIdGetter.apply(levelRule);
            upPtgValue = upPtgValue.add(nullAsZero(baseValue));
            maxIndex = bigDecimalToIntUp(multiplyPtg(userQty, upPtgValue));
            maxIndex = Math.min(maxIndex, ptgBeans.size());
            if (maxIndex < 1) {
                continue;
            }
            BigDecimal upSortValue = ptgBeans.get(maxIndex - 1).getSortValue();
            levelScore.put(levelId, upSortValue);
            if (StringUtils.isNotEmpty(beforeLevelId) && (upSortValue == null || upSortValue.compareTo(levelScore.getOrDefault(beforeLevelId, upSortValue)) == 0)) {
                //空值，或者等于前一个值，就是分层阈值无效
                validScore = false;
            }
            beforeLevelId = levelId;
            for (int bi = 0; bi < ptgBeans.size(); bi++) {
                DecimalPtgBean ptgBean = ptgBeans.get(bi);
                if (!ptgBean.isAllocated() && (bi < maxIndex || Objects.equals(ptgBean.getSortValue(), upSortValue))) {
                    ptgBean.setAllocated(true);
                    ptgBean.setLevelId(levelId);
                    levelConsumer.accept(ptgBean, levelRule);
                }
            }
        }
        ptgBeans.forEach(item -> {
            if (!item.isAllocated()) {
                item.setAllocated(true);
                item.setLevelId(defaultLevelId);
                levelConsumer.accept(item, defaultLevel);
            }
        });
        thresholdDto.setInvalidScore(CommonUtils.bool2Int(!validScore));
        thresholdDto.setLevelScore(levelScore);
        thresholdDto.setDefaultLevelId(defaultLevelId);
        return thresholdDto;
    }

    private int bigDecimalToIntUp(BigDecimal decimal) {
        if (decimal == null) {
            return 0;
        }
        return decimal.setScale(0, RoundingMode.UP).intValue();
    }

    private void queryUserRefData(CalcResultRefContext refContext, List<String> userIds) {
        String orgId = refContext.orgId;
        String xpdId = refContext.xpdId;
        refContext.refIndicatorMap.forEach((key, refIndicator) -> {
            int refType = refIndicator.refType;
            String refId = refIndicator.refId;
            boolean isPerfByScore;
            List<XpdIndicatorResultDto> resultList = null;
            List<String> indicatorIds = refIndicator.indicatorMap.values().stream()
                .map(RefIndicatorInfo::getSdIndicatorId)
                .collect(Collectors.toList());
            if (refType == DimRuleCalcRefEnum.AOM_ACT.getCode()) {
                List<String> doneUserIds = baseActivityResultRepo.doneUserIds(orgId,
                    refContext.aomPrjId, refId, userIds);
                refContext.refDoneUserMap.put(doneMainKey(refType, refId), new HashSet<>(doneUserIds));
                //查询已完成的活动结果
                resultList = activityObjectiveResultRepo.queryByUserIds(orgId, refId, doneUserIds, indicatorIds);
                Integer perfEvalType = Optional.ofNullable(refContext.aomActvMap.get(refId))
                    .map(AomActvExtBO::getPerfExtDto)
                    .map(PerfExtDto::getEvalType).orElse(null);
                isPerfByScore = PerfEvalTypeEnum.byScore(perfEvalType);
            } else {
                isPerfByScore = false;
                if (refType == DimRuleCalcRefEnum.IMPORT_DATA.getCode()) {
                    resultList = xpdImportIndicatorUserRepo.queryByUserIds(orgId, xpdId,
                        refIndicator.refId, userIds, indicatorIds);
                } else if (refType == DimRuleCalcRefEnum.PRI_PROFILE.getCode()) {
                    resultList = dwdUserIndicatorResultRepo.listUserIndicator(orgId, userIds, indicatorIds);
                }
            }
            if (resultList != null) {
                resultList.forEach(indResult -> {
                    DimCalcUserResultDto userResult = new DimCalcUserResultDto();
                    userResult.setUserId(indResult.getUserId());
                    userResult.setScoreValue(indResult.getScore());
                    userResult.setQualified(CommonUtils.isTrue(indResult.getQualified()));
                    ActvResultExtDto
                        extDto = CommonUtils.tryParseObject(indResult.getExtJson(), ActvResultExtDto.class);
                    if (extDto != null) {
                        Optional.ofNullable(extDto.getResultConfId()).filter(StringUtils::isNotEmpty)
                            .ifPresent(perfLevelId -> userResult.setLevelValues(Lists.newArrayList(perfLevelId)));
                        userResult.setEvalScoreMap(extDto.getEvalDimScore());
                        userResult.setPerfSummary(String.join(";", BeanCopierUtil.convertList(extDto.getUserPeriodResults(),
                            item -> item.getPeriodName() + ":" + (isPerfByScore ? item.getScore() : item.getGradeName()))));
                    }
                    refContext.addRefUserResult(refType, refId, indResult.getSdIndicatorId(), userResult);
                });
            }
        });
    }

    private void calcNotUsedIndicatorTotalScore(CalcResultRefContext refContext) {
        Map<String, List<BigDecimal>> totalScoreMap = getNotUsedIndicatorTotalScoreMap(refContext);
        if (totalScoreMap == null) {
            return;
        }
        List<XpdResultIndicatorPO> resultIndList = new ArrayList<>();
        totalScoreMap.forEach((sdIndicatorId, scoreList) -> {
            BigDecimal totalScore = BigDecimal.ZERO;
            for (BigDecimal score : scoreList) {
                totalScore = totalScore.add(score);
            }
            XpdResultIndicatorPO indicatorPO = new XpdResultIndicatorPO();
            indicatorPO.initData(null);
            indicatorPO.setOrgId(refContext.orgId);
            indicatorPO.setXpdId(refContext.xpdId);
            indicatorPO.setSdIndicatorId(sdIndicatorId);
            BigDecimal scoreTotal = totalScore.divide(BigDecimal.valueOf(scoreList.size()), MC_TWO.getPrecision(), MC_TWO.getRoundingMode());
            indicatorPO.setScoreTotal(ScoreSystemEnum.getMaxScore(refContext.scoreSystem, scoreTotal));
            indicatorPO.setScoreAvg(BigDecimal.ZERO);
            indicatorPO.setQualifiedPtg(BigDecimal.ZERO);
            resultIndList.add(indicatorPO);
        });
        xpdResultIndicatorMapper.deleteByXpdSdIndicatorIds(refContext.orgId, refContext.xpdId, refContext.notUsedIndicatorIds);
        BatchOperationUtil.batchSave(resultIndList, xpdResultIndicatorMapper::insertBatchSomeColumn);
    }

    @Nullable
    private static Map<String, List<BigDecimal>> getNotUsedIndicatorTotalScoreMap(CalcResultRefContext refContext) {
        Map<String, List<BigDecimal>> totalScoreMap = new HashMap<>();
        Set<String> notUsedIndicatorIds = refContext.notUsedIndicatorIds;
        if (CollectionUtils.isEmpty(notUsedIndicatorIds)) {
            return null;
        }
        for (AomActvExtBO actvExt : refContext.aomActvMap.values()) {
            if (CollectionUtils.isEmpty(actvExt.getIndicators())) {
                continue;
            }
            actvExt.getIndicators().forEach(indicator -> {
                if (notUsedIndicatorIds.contains(indicator.getSdIndicatorId())) {
                    IArrayUtils.addMapList(totalScoreMap,
                        indicator.getSdIndicatorId(),
                        Optional.ofNullable(indicator.getTotalScore()).orElse(BigDecimal.ZERO));
                }
            });
        }
        refContext.xpdImportMap.values().stream()
            .filter(importPO -> XpdImportTypeEnum.DIM_INDICATOR.getCode().equals(importPO.getImportType()))
            .forEach(importPO -> {
                Set<String> actvIndicatorSet = Optional.ofNullable(refContext.dimIndicatorsMap.get(importPO.getSdDimId()))
                    .map(Pair::getValue).map(list -> list.stream().map(IndicatorDto::getItemId).collect(Collectors.toSet()))
                    .orElse(new HashSet<>());
                BigDecimal totalScore = Optional.ofNullable(importPO.getScoreTotal()).orElse(BigDecimal.ZERO);
                notUsedIndicatorIds.stream().filter(actvIndicatorSet::contains)
                    .forEach(indicatorId -> IArrayUtils.addMapList(totalScoreMap, indicatorId, totalScore));
            });
        return totalScoreMap;
    }

    private void calcNotUsedIndicatorUserResult(CalcResultRefContext refContext, List<String> userIds) {
        Set<String> notUsedIndicatorIds = refContext.notUsedIndicatorIds;
        if (CollectionUtils.isEmpty(notUsedIndicatorIds)) {
            return;
        }
        String orgId = refContext.orgId;
        String xpdId = refContext.xpdId;
        Map<String, Integer> indicatorCount = new HashMap<>();
        Map<String, UserIndicatorResults> userResultMap = new HashMap<>(userIds.size());
        Set<String> dupResultKeySet = new HashSet<>(userIds.size());
        BiConsumer<XpdIndicatorResultDto, DimUserIndicatorResultDto> addUserResultFunc = (userResult, resultDto) -> {
            String resultKey = userResult.getUserId() + StringPool.DOT + userResult.getSdIndicatorId();
            String dupResultKey = resultKey + StringPool.DOT + resultDto.getRefType() + StringPool.DOT + resultDto.getRefId();
            //如果有重复结果取第一条
            if (!dupResultKeySet.contains(dupResultKey)) {
                dupResultKeySet.add(dupResultKey);
                UserIndicatorResults userResults = userResultMap.get(resultKey);
                if (userResults == null) {
                    userResults = new UserIndicatorResults();
                    userResults.userId = userResult.getUserId();
                    userResults.sdIndicatorId = userResult.getSdIndicatorId();
                    userResultMap.put(resultKey, userResults);
                }
                userResults.resultList.add(resultDto);
            }
        };
        //查出活动指标结果
        for (AomActvExtBO actvExt : refContext.aomActvMap.values()) {
            if (CollectionUtils.isEmpty(actvExt.getIndicators())) {
                continue;
            }
            Set<String> actvIndicatorSet = actvExt.getIndicators().stream()
                .map(AomActvIndicatorDto::getSdIndicatorId)
                .collect(Collectors.toSet());
            List<String> actvNotUseIndicatorIds = notUsedIndicatorIds.stream().filter(actvIndicatorSet::contains)
                .collect(Collectors.toList());
            if (actvNotUseIndicatorIds.isEmpty()) {
                continue;
            }
            actvNotUseIndicatorIds.forEach(sdIndicatorId -> {
                int count = indicatorCount.getOrDefault(sdIndicatorId, 0);
                count++;
                indicatorCount.put(sdIndicatorId, count);
            });
            String actvRefId = actvExt.getActvRefId();
            List<String> doneUserIds = baseActivityResultRepo.doneUserIds(orgId,
                refContext.aomPrjId, actvRefId, userIds);
            //查询已完成的活动结果
            List<XpdIndicatorResultDto> resultList = activityObjectiveResultRepo.queryByUserIds(orgId, actvRefId, doneUserIds, actvNotUseIndicatorIds);
            Map<String, XpdIndicatorResultDto> userIndicatorMap = StreamUtil.list2map(resultList,
                actvResult -> actvResult.getUserId() + StringPool.DOT + actvResult.getSdIndicatorId());
            for (String userId : doneUserIds) {
                for (String sdIndicatorId : actvNotUseIndicatorIds) {
                    //没有结果，按得分0填充
                    XpdIndicatorResultDto userResult = userIndicatorMap.getOrDefault(userId + StringPool.DOT + sdIndicatorId,
                        new XpdIndicatorResultDto());
                    userResult.setUserId(userId);
                    userResult.setSdIndicatorId(sdIndicatorId);
                    DimUserIndicatorResultDto resultDto = new DimUserIndicatorResultDto();
                    resultDto.setRefType(DimRuleCalcRefEnum.AOM_ACT.getCode());
                    resultDto.setRefId(actvRefId);
                    resultDto.setScoreValue(Optional.ofNullable(userResult.getScore()).orElse(BigDecimal.ZERO));
                    resultDto.setQualified(CommonUtils.isTrue(userResult.getQualified()));
                    addUserResultFunc.accept(userResult, resultDto);
                }
            }
        }
        //查出导入指标结果
        refContext.xpdImportMap.values().stream()
            .filter(importPO -> XpdImportTypeEnum.DIM_INDICATOR.getCode().equals(importPO.getImportType()))
            .forEach(importPO -> {
                Set<String> actvIndicatorSet = Optional.ofNullable(refContext.dimIndicatorsMap.get(importPO.getSdDimId()))
                    .map(Pair::getValue).map(list -> list.stream().map(IndicatorDto::getItemId).collect(Collectors.toSet()))
                    .orElse(new HashSet<>());
                List<String> actvNotUseIndicatorIds = notUsedIndicatorIds.stream().filter(actvIndicatorSet::contains)
                    .collect(Collectors.toList());
                actvNotUseIndicatorIds.forEach(sdIndicatorId -> {
                    int count = indicatorCount.getOrDefault(sdIndicatorId, 0);
                    count++;
                    indicatorCount.put(sdIndicatorId, count);
                });
                List<XpdIndicatorResultDto> resultList = xpdImportIndicatorUserRepo.queryByUserIds(orgId, xpdId,
                    importPO.getId(), userIds, actvNotUseIndicatorIds);
                Map<String, XpdIndicatorResultDto> userIndicatorMap = StreamUtil.list2map(resultList,
                    importResult -> importResult.getUserId() + StringPool.DOT + importResult.getSdIndicatorId());
                for (String userId : userIds) {
                    for (String sdIndicatorId : actvNotUseIndicatorIds) {
                        //导入没有结果，按得分0填充
                        XpdIndicatorResultDto userResult = userIndicatorMap.getOrDefault(userId + StringPool.DOT + sdIndicatorId,
                            new XpdIndicatorResultDto());
                        userResult.setUserId(userId);
                        userResult.setSdIndicatorId(sdIndicatorId);
                        DimUserIndicatorResultDto resultDto = new DimUserIndicatorResultDto();
                        resultDto.setRefType(DimRuleCalcRefEnum.IMPORT_DATA.getCode());
                        resultDto.setRefId(importPO.getId());
                        resultDto.setScoreValue(Optional.ofNullable(userResult.getScore()).orElse(BigDecimal.ZERO));
                        resultDto.setQualified(CommonUtils.isTrue(userResult.getQualified()));
                        addUserResultFunc.accept(userResult, resultDto);
                    }
                }
            });

        Map<String, List<BigDecimal>> notUsedIndicatorTotalScoreMap = getNotUsedIndicatorTotalScoreMap(refContext);

        List<XpdResultUserIndicatorPO> userIndicatorList = new ArrayList<>();
        userResultMap.forEach((key, userResults) -> {
            boolean qualified = false;
            BigDecimal scoreValue = BigDecimal.ZERO;
            for (DimUserIndicatorResultDto indicatorResultDto : userResults.resultList) {
                if (Optional.ofNullable(indicatorResultDto.getQualified()).orElse(false)) {
                    qualified = true;
                }
                if (indicatorResultDto.getScoreValue() != null) {
                    scoreValue = scoreValue.add(indicatorResultDto.getScoreValue());
                }
            }
            XpdResultUserIndicatorPO userIndResult = refContext.newResultUserIndicatorPO(userResults.userId);
            userIndResult.setSdIndicatorId(userResults.sdIndicatorId);

            // 计算总分
            BigDecimal totalScore = BigDecimal.ZERO;
            int size = 0;
            if (notUsedIndicatorTotalScoreMap != null && notUsedIndicatorTotalScoreMap.containsKey(userResults.sdIndicatorId)) {
                List<BigDecimal> scoreList = notUsedIndicatorTotalScoreMap.get(userResults.sdIndicatorId);
                if (!scoreList.isEmpty()) {
                    size = scoreList.size();
                    totalScore = scoreList.stream()
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
            }

            // 计算平均总分,避免除0异常
            BigDecimal avgTotalScore = size > 0
                ? totalScore.divide(BigDecimal.valueOf(size), MC_TWO.getPrecision(), MC_TWO.getRoundingMode())
                : BigDecimal.ZERO;

            // 计算平均得分,避免除0异常
            int indicatorSize = Optional.ofNullable(indicatorCount.get(userResults.sdIndicatorId)).orElse(0);
            BigDecimal avgScore = indicatorSize > 0
                ? scoreValue.divide(BigDecimal.valueOf(indicatorSize), MC_TWO.getPrecision(), MC_TWO.getRoundingMode())
                : BigDecimal.ZERO;

            // 转换为目标分值
            userIndResult.setScoreValue(convertToTargetSystem(avgScore, avgTotalScore, refContext.scoreSystem));
            userIndResult.setQualified(CommonUtils.bool2Int(qualified));
            userIndResult.setResultDetail(JSON.toJSONString(userResults.resultList));
            userIndicatorList.add(userIndResult);
        });
        saveUserIndResultList(refContext, userIndicatorList);
    }

    private void calcDimUserResult(List<DimRuleMetaDTO> dimRuleList, CalcResultRefContext refContext, List<String> userIds) {
        for (DimRuleMetaDTO dimRuleMetaDTO : dimRuleList) {
            if (dimRuleMetaDTO.coverByImport) {
                //sync import dim result
                xpdImportDimUserMapper.getBySdDimIdAndUserIds(refContext.orgId, refContext.xpdId,
                    dimRuleMetaDTO.sdDimId, userIds).forEach(importResult -> {
                    XpdResultUserDimPO userDimPO = refContext.newResultUserDimPO(dimRuleMetaDTO.sdDimId, importResult.getUserId());
                    userDimPO.setGridLevelId(importResult.getGridLevelId());
                    refContext.userDimList.add(userDimPO);
                });
            } else if (dimRuleMetaDTO.dimRuleBase != null) {
                if (Objects.equals(dimRuleMetaDTO.dimRuleBase.getCalcType(), DimCalcTypeEnum.PERF_RESULT.getCode())) {
                    // 按绩效等级
                    addPrefDimResult(dimRuleMetaDTO, refContext, userIds, true);
                } else if (Objects.equals(dimRuleMetaDTO.dimRuleBase.getCalcType(), DimCalcTypeEnum.PERF_SCORE.getCode())) {
                    // 按绩效得分
                    addPrefDimResult(dimRuleMetaDTO, refContext, userIds, false);
                } else if (Objects.equals(dimRuleMetaDTO.dimRuleBase.getCalcType(), DimCalcTypeEnum.INDICATOR.getCode())) {
                    if (Objects.equals(DimCalcRuleEnum.FORMULA.getCode(), dimRuleMetaDTO.dimRuleBase.getCalcRule())) {
                        //高级公式分支
                        addFormulaResult(dimRuleMetaDTO, refContext, dimRuleMetaDTO.dimRuleBase.getFormulaCalc(), userIds);
                    } else {
                        addIndicatorResult(dimRuleMetaDTO, refContext, userIds);
                    }
                } else {
                    //子维度结果
                    addSubDimensionResult(dimRuleMetaDTO, refContext, userIds);
                }
                //一个用户一个维度只有一条
                Map<String, String> userDimResultMap = IArrayUtils.listAsMap(refContext.userDimList, XpdResultUserDimPO::getUserId, XpdResultUserDimPO::getId);
                refContext.userIndicatorList.forEach(item -> {
                    item.setResultDimId(Optional.ofNullable(userDimResultMap.get(item.getUserId())).orElse(StringPool.EMPTY));
                });
            }
            saveUserDimAndIndResult(refContext);
        }
    }

    private void saveUserDimAndIndResult(CalcResultRefContext refContext) {
        //存在的更新，这里要把ResultDimId替换下
        Map<String, String> gen2ExistIdMap = new HashMap<>();
        IArrayUtils.list2Map(refContext.userDimList,
            XpdResultUserDimPO::getSdDimId).forEach((sdDimId, list) -> {
            List<UserResultIdDTO> existUserIds = xpdResultUserDimMapper.queryIgnoreDelByDimUserIds(refContext.orgId,
                refContext.xpdId, sdDimId, BeanCopierUtil.convertList(list, XpdResultUserDimPO::getUserId));
            Map<String, String> userId2PkMap = IArrayUtils.listAsMap(existUserIds, UserResultIdDTO::getUserId, UserResultIdDTO::getId);
            List<XpdResultUserDimPO> updateList = new ArrayList<>();
            List<XpdResultUserDimPO> insertList = new ArrayList<>();
            list.forEach(userDim -> {
                String existId = userId2PkMap.get(userDim.getUserId());
                if (StringUtils.isEmpty(existId)) {
                    insertList.add(userDim);
                } else {
                    gen2ExistIdMap.put(userDim.getId(), existId);
                    userDim.setId(existId);
                    updateList.add(userDim);
                }
            });
            BatchOperationUtil.batchSave(insertList, xpdResultUserDimMapper::insertBatchSomeColumn);
            BatchOperationUtil.batchSave(updateList, xpdResultUserDimMapper::batchUpdateResult);
        });
        //ResultDimId替换
        refContext.userIndicatorList.forEach(item
            -> item.setResultDimId(gen2ExistIdMap.getOrDefault(item.getResultDimId(), item.getResultDimId())));
        saveUserIndResultList(refContext, refContext.userIndicatorList);
        refContext.userDimList.clear();
        refContext.userIndicatorList.clear();
    }

    private void saveUserIndResultList(CalcResultRefContext refContext, List<XpdResultUserIndicatorPO> userIndicatorList) {
        IArrayUtils.list2Map(userIndicatorList,
            XpdResultUserIndicatorPO::getSdIndicatorId).forEach((sdIndicatorId, list) -> {
            List<UserResultIdDTO> existUserIds = xpdResultUserIndicatorMapper.queryIgnoreDelByIndUserIds(refContext.orgId,
                refContext.xpdId, sdIndicatorId, BeanCopierUtil.convertList(list, XpdResultUserIndicatorPO::getUserId));
            Map<String, String> userId2PkMap = IArrayUtils.listAsMap(existUserIds, UserResultIdDTO::getUserId, UserResultIdDTO::getId);
            List<XpdResultUserIndicatorPO> updateList = new ArrayList<>();
            List<XpdResultUserIndicatorPO> insertList = new ArrayList<>();
            list.forEach(userInd -> {
                String existId = userId2PkMap.get(userInd.getUserId());
                if (StringUtils.isEmpty(existId)) {
                    insertList.add(userInd);
                } else {
                    userInd.setId(existId);
                    updateList.add(userInd);
                }
            });
            BatchOperationUtil.batchSave(insertList, xpdResultUserIndicatorMapper::insertBatchSomeColumn);
            BatchOperationUtil.batchSave(updateList, xpdResultUserIndicatorMapper::batchUpdateResult);
        });
    }

    private void addFormulaResult(
        DimRuleMetaDTO dimRuleMetaDTO, CalcResultRefContext refContext,
        ExpressionCalc formulaCalc, List<String> userIds) {
        if (formulaCalc == null) {
            return;
        }
        for (String userId : userIds) {
            BigDecimal scoreVal = formulaResult(refContext, formulaCalc, Pair.of(CALC_USER, userId));
            if (scoreVal != null) {
                XpdResultUserDimPO userDimPO = refContext.newResultUserDimPO(dimRuleMetaDTO.sdDimId, userId);
                userDimPO.setScoreValue(convertToTargetSystem(scoreVal, dimRuleMetaDTO.dimRuleBase.getCalcTotalScore(), refContext.scoreSystem));
                userDimPO.setQualifiedPtg(BigDecimal.ZERO);
                DimGridLevelRuleDTO levelRule = minMatchLevelRule(
                    dimRuleMetaDTO.dimRuleBase.getGridLevelRules(),
                    DimGridLevelRuleDTO::getBaseValue,
                    userDimPO.getScoreValue());
                if (levelRule != null || dimRuleMetaDTO.levelTypeAfterResult()) {
                    userDimPO.setGridLevelId(Optional.ofNullable(levelRule).map(DimGridLevelRuleDTO::getGridLevelId).orElse(StringPool.EMPTY));
                    refContext.userDimList.add(userDimPO);
                }
            }
        }
    }

    private void addSubDimensionResult(DimRuleMetaDTO dimRuleMetaDTO, CalcResultRefContext refContext, List<String> userIds) {
        boolean hasSubDim = CollectionUtils.isNotEmpty(dimRuleMetaDTO.subDimRules);
        boolean hasRuleList = CollectionUtils.isNotEmpty(dimRuleMetaDTO.ruleCalcList);
        for (String userId : userIds) {
            DimResult dimResult = null;
            if (hasSubDim) {
                dimResult = calcSubDimensionResult(dimRuleMetaDTO.subDimRules, refContext, Pair.of(CALC_USER, userId));
            } else if (hasRuleList) {
                dimResult = calcDimByIndicatorResult(refContext, dimRuleMetaDTO.ruleCalcList,
                    new CalcDimResultParam(true, refContext.calcDimByScore()), Pair.of(CALC_USER, userId));
            }
            if (dimResult != null && !dimResult.hasNoResult) {
                XpdResultUserDimPO userDimPO = refContext.newResultUserDimPO(dimRuleMetaDTO.sdDimId, userId);
                userDimPO.setScoreValue(dimResult.score);
                userDimPO.setQualifiedPtg(dimResult.qualifiedPtg);
                DimGridLevelRuleDTO levelRule = minMatchLevelRule(dimRuleMetaDTO.dimRuleBase.getGridLevelRules(), DimGridLevelRuleDTO::getBaseValue,
                        refContext.resultType == DimResultTypeEnum.SCORE_VALUE.getCode() ? userDimPO.getScoreValue() : userDimPO.getQualifiedPtg());
                if (levelRule != null || dimRuleMetaDTO.levelTypeAfterResult()) {
                    userDimPO.setGridLevelId(Optional.ofNullable(levelRule).map(DimGridLevelRuleDTO::getGridLevelId).orElse(StringPool.EMPTY));
                    refContext.userDimList.add(userDimPO);
                }
            }
        }
    }

    /**
     * 下穿无结果时会返回null
     * @param list
     * @param refContext
     * @param targetPair
     * @return
     */
    private DimResult calcSubDimensionResult(List<DimRuleMetaDTO> list, CalcResultRefContext refContext, Pair<Integer, String> targetPair) {
        DimResult result = new DimResult();
        result.score = BigDecimal.ZERO;
        result.qualifiedPtg = BigDecimal.ZERO;
        for (DimRuleMetaDTO dimRuleMetaDTO : list) {
            if (dimRuleMetaDTO.dimRuleBase == null) {
                continue;
            }
            DimResult dimResult = null;
            if (CollectionUtils.isNotEmpty(dimRuleMetaDTO.subDimRules)) {
                dimResult = calcSubDimensionResult(dimRuleMetaDTO.subDimRules, refContext, targetPair);
            } else if (CollectionUtils.isNotEmpty(dimRuleMetaDTO.ruleCalcList)) {
                dimResult = calcDimByIndicatorResult(refContext, dimRuleMetaDTO.ruleCalcList,
                    new CalcDimResultParam(true, refContext.calcDimByScore()), targetPair);
            } else {
                //两个都为空就是脏数据
                log.debug("LOG20723:");
                continue;
            }
            if (dimResult == null || dimResult.hasNoResult) {
                return null;
            }
            BigDecimal weight = dimRuleMetaDTO.dimRuleBase.getWeight();
            if (weight != null) {
                result.score = result.score.add(multiplyPtg(dimResult.score, weight));
            }
            result.qualifiedPtg = result.qualifiedPtg.add(dimResult.qualifiedPtg);
            if (targetPair.getKey() == CALC_TOTAL_SCORE) {
                dimRuleMetaDTO.dimRuleBase.setCalcTotalScore(dimResult.getScore());
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            result.qualifiedPtg = result.qualifiedPtg
                .divide(BigDecimal.valueOf(list.size()), MC_TWO.getPrecision(), MC_TWO.getRoundingMode());
        }
        return result;
    }

    private BigDecimal multiplyPtg(BigDecimal val, BigDecimal ptg) {
        if (val == null || ptg == null) {
            return BigDecimal.ZERO;
        }
        return val.multiply(ptg).divide(AppConstants.HUNDRED);
    }

    private void addIndicatorResult(DimRuleMetaDTO dimRuleMetaDTO, CalcResultRefContext refContext, List<String> userIds) {
        if (CollectionUtils.isEmpty(dimRuleMetaDTO.ruleCalcList)) {
            return;
        }
        for (String userId : userIds) {
            DimResult dimResult = calcDimByIndicatorResult(refContext, dimRuleMetaDTO.ruleCalcList,
                new CalcDimResultParam(true, refContext.calcDimByScore()), Pair.of(CALC_USER, userId));
            if (!dimResult.hasNoResult) {
                XpdResultUserDimPO userDimPO = refContext.newResultUserDimPO(dimRuleMetaDTO.sdDimId, userId);
                userDimPO.setScoreValue(dimResult.score);
                userDimPO.setQualifiedPtg(dimResult.qualifiedPtg);
                DimGridLevelRuleDTO levelRule = minMatchLevelRule(dimRuleMetaDTO.dimRuleBase.getGridLevelRules(), DimGridLevelRuleDTO::getBaseValue,
                        refContext.resultType == DimResultTypeEnum.SCORE_VALUE.getCode() ? userDimPO.getScoreValue() : userDimPO.getQualifiedPtg());
                if (levelRule != null || dimRuleMetaDTO.levelTypeAfterResult()) {
                    userDimPO.setGridLevelId(Optional.ofNullable(levelRule).map(DimGridLevelRuleDTO::getGridLevelId).orElse(StringPool.EMPTY));
                    refContext.userDimList.add(userDimPO);
                }
            }
        }
    }

    private <T extends XpdIndicatorCalcBase> DimResult calcDimByIndicatorResult(
        CalcResultRefContext refContext,
        List<T> ruleCalcList,
        CalcDimResultParam calcParam, Pair<Integer, String> targetPair) {
        //calc checked
        boolean hasNoResult = false;
        BigDecimal score = BigDecimal.ZERO;
        int qualifiedQty = 0;
        ruleCalcList = Optional.ofNullable(ruleCalcList).orElse(Collections.emptyList());
        // 维度配置的指标规则
        for (XpdIndicatorCalcBase ruleCalcDto : ruleCalcList) {
            IndicatorResult indResult = calcIndicatorResult(refContext, calcParam,
                ruleCalcDto.getSdIndicatorId(), ruleCalcDto.getCalcMethod(), targetPair, ruleCalcDto.getRefList());
            if (indResult.hasNoResult) {
                hasNoResult = true;
            }
            if (ruleCalcDto.getWeight() != null) {
                // 如果计算的是用户的得分，并且涉及分制转换，那么这里的score已经是转换过的分数了
                score = score.add(multiplyPtg(indResult.score, ruleCalcDto.getWeight()));
            }
            if (indResult.qualified) {
                qualifiedQty++;
            }
            if (calcParam.addIndResult && targetPair.getKey() == CALC_USER && !indResult.hasNoResult) {
                //用户指标结果
                XpdResultUserIndicatorPO userIndResult = refContext.newResultUserIndicatorPO(targetPair.getValue());
                userIndResult.setSdIndicatorId(ruleCalcDto.getSdIndicatorId());
                userIndResult.setScoreValue(indResult.score);
                userIndResult.setQualified(CommonUtils.bool2Int(indResult.qualified));
                userIndResult.setResultDetail(JSON.toJSONString(indResult.resultList));
                refContext.userIndicatorList.add(userIndResult);
            }
            if (targetPair.getKey() == CALC_TOTAL_SCORE) {
                ruleCalcDto.setTotalScore(indResult.score);
            } else if (targetPair.getKey() == CALC_STANDARD_SCORE) {
                ruleCalcDto.setStandardScore(indResult.score);
            }
        }
        DimResult dimResult = new DimResult();
        dimResult.hasNoResult = hasNoResult;
        dimResult.score = score;
        dimResult.qualifiedPtg = ruleCalcList.isEmpty() ? BigDecimal.ZERO : BigDecimal.valueOf(qualifiedQty * 100L)
                .divide(BigDecimal.valueOf(ruleCalcList.size()), MC_TWO.getPrecision(), MC_TWO.getRoundingMode());
        return dimResult;
    }

    private IndicatorResult calcIndicatorResult(CalcResultRefContext refContext,
            CalcDimResultParam calcParam, String sdIndicatorId, Integer calcMethod,
            Pair<Integer, String> targetPair,
            List<XpdDimRuleCalcRefDto> refList) {
        //calc checked
        if (calcParam.byScore) {
            return calcIndicatorScoreResult(refContext, sdIndicatorId, calcMethod, targetPair, refList);
        } else {
            return calcIndicatorQualifiedResult(refContext, sdIndicatorId, calcMethod, targetPair, refList);
        }
    }

    private IndicatorResult calcIndicatorScoreResult(CalcResultRefContext refContext,
            String sdIndicatorId, Integer calcMethod, Pair<Integer, String> targetPair,
            List<XpdDimRuleCalcRefDto> refList) {
        //calc checked
        IndicatorResult result = new IndicatorResult();
        result.score = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(refList)) {
            // 指标规则选择的盘点活动/导入数据/个人档案
            for (XpdDimRuleCalcRefDto ruleCalcRefDto : refList) {
                DimCalcUserResultDto userResult = queryTargetResult(refContext, targetPair,
                    ruleCalcRefDto.getRefType(), ruleCalcRefDto.getRefId(), sdIndicatorId);
                if (userResult == null) {
                    result.hasNoResult = true;
                    continue;
                }
                result.resultList.add(buildUserIndicatorResult(ruleCalcRefDto, userResult));
                if (userResult.getScoreValue() != null) {
                    result.score = result.score.add(userResult.getScoreValue());
                }
                if (Boolean.TRUE.equals(userResult.getQualified())) {
                    result.qualified = true;
                }
            }
            if (!Objects.equals(calcMethod, DimCalcMethodEnum.SUM.getCode())) {
                //非求和时：求平均
                result.score = result.score.divide(BigDecimal.valueOf(refList.size()), MC_TWO.getPrecision(), MC_TWO.getRoundingMode());
            }
        }
        // 转换分制
        if (targetPair.getKey() == CALC_USER && ScoreSystemEnum.isNotOriginal(refContext.scoreSystem) && result.score != null) {
            IndicatorResult indicatorTotalScore = calcIndicatorScoreResult(
                refContext, sdIndicatorId, calcMethod, Pair.of(CALC_TOTAL_SCORE, StringPool.EMPTY), refList);
            BigDecimal scoreTotal = indicatorTotalScore.score;
            result.score = convertToTargetSystem(result.score, scoreTotal, refContext.scoreSystem);
        }
        return result;
    }

    private DimCalcUserResultDto queryTargetResult(CalcResultRefContext refContext,
        Pair<Integer, String> targetPair,
        int refType, String refId, String sdIndicatorId) {
        if (targetPair.getKey() == CALC_USER) {
            if (DimRuleCalcRefEnum.AOM_ACT.getCode() == refType) {
                return refContext.queryAomUserResult(refContext, refId, sdIndicatorId, targetPair.getValue());
            } else {
                return refContext.queryUserResult(refType, refId, sdIndicatorId, targetPair.getValue());
            }
        } else {
            RefIndicatorInfo indicatorInfo = refContext.queryIndicatorInfo(refType, refId, sdIndicatorId);
            if (targetPair.getKey() == CALC_TOTAL_SCORE) {
                DimCalcUserResultDto resultDto = new DimCalcUserResultDto();
                resultDto.setQualified(true);
                resultDto.setScoreValue(nullAsZero(indicatorInfo, RefIndicatorInfo::getTotalScore));
                return resultDto;
            } else if (indicatorInfo != null && indicatorInfo.getStandardScore() != null) {
                DimCalcUserResultDto resultDto = new DimCalcUserResultDto();
                resultDto.setQualified(true);
                resultDto.setScoreValue(indicatorInfo.getStandardScore());
                return resultDto;
            }
        }
        return null;
    }

    private IndicatorResult calcIndicatorQualifiedResult(CalcResultRefContext refContext,
            String sdIndicatorId, Integer calcMethod, Pair<Integer, String> targetPair,
            List<XpdDimRuleCalcRefDto> refList) {
        //calc checked
        IndicatorResult result = new IndicatorResult();
        result.score = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(refList)) {
            int qualifiedQty = 0;
            for (XpdDimRuleCalcRefDto ruleCalcRefDto : refList) {
                DimCalcUserResultDto userResult = queryTargetResult(refContext, targetPair,
                    ruleCalcRefDto.getRefType(), ruleCalcRefDto.getRefId(), sdIndicatorId);
                if (userResult == null) {
                    result.hasNoResult = true;
                    continue;
                }
                result.resultList.add(buildUserIndicatorResult(ruleCalcRefDto, userResult));
                if (userResult.getScoreValue() != null) {
                    result.score = result.score.add(userResult.getScoreValue());
                }
                if (Boolean.TRUE.equals(userResult.getQualified())) {
                    qualifiedQty++;
                }
            }
            //默认求平均
            result.score = result.score.divide(BigDecimal.valueOf(refList.size()), MC_TWO.getPrecision(), MC_TWO.getRoundingMode());
            if (Objects.equals(calcMethod, DimCalcMethodEnum.QUALIFIED_ALL.getCode())) {
                result.qualified = qualifiedQty >= refList.size();
            } else {
                result.qualified = qualifiedQty > 0;
            }
            if (result.qualified) {
                result.hasNoResult = false;
            }
        }
        // 转换分制
        if (targetPair.getKey() == CALC_USER && ScoreSystemEnum.isNotOriginal(refContext.scoreSystem) && result.score != null) {
            IndicatorResult indicatorTotalScore = calcIndicatorQualifiedResult(
                refContext, sdIndicatorId, calcMethod, Pair.of(CALC_TOTAL_SCORE, StringPool.EMPTY), refList);
            BigDecimal scoreTotal = indicatorTotalScore.score;
            result.score = convertToTargetSystem(result.score, scoreTotal, refContext.scoreSystem);
        }
        return result;
    }

    private DimUserIndicatorResultDto buildUserIndicatorResult(XpdDimRuleCalcRefDto ruleCalcRefDto, DimCalcUserResultDto userResult) {
        DimUserIndicatorResultDto resultDto = new DimUserIndicatorResultDto();
        resultDto.setRefType(ruleCalcRefDto.getRefType());
        resultDto.setRefId(ruleCalcRefDto.getRefId());
        resultDto.setLevelValues(userResult.getLevelValues());
        resultDto.setScoreValue(userResult.getScoreValue());
        resultDto.setQualified(userResult.getQualified());
        return resultDto;
    }

    private void addPrefDimResult(DimRuleMetaDTO dimRuleMetaDTO, CalcResultRefContext refContext, List<String> userIds, boolean byContainsLevel) {
        for (String userId : userIds) {
            DimCalcUserResultDto userResultDto = refContext.queryAomUserResult(refContext, dimRuleMetaDTO.dimRuleBase.getAomActId(),
                    dimRuleMetaDTO.dimRuleBase.getSdIndicatorId(), userId);
            if (userResultDto == null) {
                continue;
            }

            // 转换分制
            BigDecimal userScore = nullAsZero(userResultDto.getScoreValue());
            BigDecimal originUserScore = userScore;
            if (!byContainsLevel && ScoreSystemEnum.isNotOriginal(refContext.scoreSystem)) {
                // 按绩效得分计算时，需要转换分制
                BigDecimal calcTotalScore = dimRuleMetaDTO.dimRuleBase.getCalcTotalScore();
                log.debug("LOG20763:userScore={},totalScore={}", userScore, calcTotalScore);
                userScore = convertToTargetSystem(userScore, calcTotalScore, refContext.scoreSystem);
//                // 直接覆盖更新有副作用
//                userResultDto.setScoreValue(userScore);
            }

            //calc checked
            DimGridLevelRuleDTO matchLevel = calcPerfMatchLevel(dimRuleMetaDTO, refContext, userResultDto, byContainsLevel);
            XpdResultUserIndicatorPO userIndResult = refContext.newResultUserIndicatorPO(userId);
            userIndResult.setSdIndicatorId(dimRuleMetaDTO.dimRuleBase.getSdIndicatorId());
            userIndResult.setPerfResultId(IArrayUtils.getFirst(userResultDto.getLevelValues()));
            userIndResult.setPerfSummary(userResultDto.getPerfSummary());
            userIndResult.setScoreValue(userScore);
            userIndResult.setQualified(CommonUtils.bool2Int(userResultDto.getQualified()));
            DimUserIndicatorResultDto indResultDto = new DimUserIndicatorResultDto();
            indResultDto.setRefType(DimRuleCalcRefEnum.AOM_ACT.getCode());
            indResultDto.setRefId(dimRuleMetaDTO.dimRuleBase.getAomActId());
            indResultDto.setScoreValue(originUserScore); // 记录原始指标得分快照，无需转换分制
            indResultDto.setQualified(CommonUtils.isTrue(userIndResult.getQualified()));
            userIndResult.setResultDetail(JSON.toJSONString(Lists.newArrayList(indResultDto)));
            refContext.userIndicatorList.add(userIndResult);
            //matchLevel为null时，按分数并且百分比则保存维度结果，为后续百分比计算用
            if (matchLevel != null || (!byContainsLevel && dimRuleMetaDTO.levelTypeAfterResult())) {
                XpdResultUserDimPO userDimPO = refContext.newResultUserDimPO(dimRuleMetaDTO.sdDimId, userId);
                userDimPO.setGridLevelId(Optional.ofNullable(matchLevel).map(DimGridLevelRuleDTO::getGridLevelId).orElse(StringPool.EMPTY));
                userDimPO.setScoreValue(nullAsZero(userScore));
                userDimPO.setQualifiedPtg(Optional.ofNullable(userResultDto.getQualified()).orElse(false)
                        ? AppConstants.HUNDRED : BigDecimal.ZERO);
                userDimPO.setPerfResultId(IArrayUtils.getFirst(userResultDto.getLevelValues()));
                refContext.userDimList.add(userDimPO);
            }
        }
    }

    private DimGridLevelRuleDTO calcPerfMatchLevel(DimRuleMetaDTO dimRuleMetaDTO, CalcResultRefContext refContext,
        DimCalcUserResultDto userResultDto, boolean byContainsLevel) {
        DimGridLevelRuleDTO matchLevel;
        if (byContainsLevel) {
            //按绩效结果计算
            matchLevel = IArrayUtils.getFirstMatch(dimRuleMetaDTO.dimRuleBase.getGridLevelRules(), levelRule -> {
                if (CollectionUtils.isNotEmpty(levelRule.getMatchValues())) {
                    List<String> userLevelValues = userResultDto.getLevelValues();
                    if (userLevelValues != null) {
                        for (String matchValue : levelRule.getMatchValues()) {
                            if (userLevelValues.contains(matchValue)) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            });
        } else {
            //按绩效得分计算
            BigDecimal calcTotalScore = dimRuleMetaDTO.dimRuleBase.getCalcTotalScore();
            BigDecimal userScore = convertToTargetSystem(userResultDto.getScoreValue(), calcTotalScore, refContext.scoreSystem);
            matchLevel = minMatchLevelRule(dimRuleMetaDTO.dimRuleBase.getGridLevelRules(), DimGridLevelRuleDTO::getBaseValue, userScore);
        }
        return matchLevel;
    }

    private <T> T minMatchLevelRule(List<T> levelRules, Function<T, BigDecimal> baseValGetter, BigDecimal userValue) {
        if (levelRules != null && userValue != null) {
            T matchRule = null;
            for (T levelRule : levelRules) {
                //得分≥设置分数
                BigDecimal baseVal = baseValGetter.apply(levelRule);
                if (baseVal != null && userValue.compareTo(baseVal) >= 0) {
                    if (matchRule == null || baseVal.compareTo(baseValGetter.apply(matchRule)) > 0) {
                        matchRule = levelRule;
                    }
                }
            }
            return matchRule;
        }
        return null;
    }

    private List<DimRuleMetaDTO> buildRuleList(XpdPO xpdPO, List<XpdImportPO> importPOList) {
        String orgId = xpdPO.getOrgId();
        String xpdId = xpdPO.getId();
        List<XpdDimPO> xpdDimList = xpdDimMapper.listByXpdId(orgId, xpdId);
        List<String> importSdDimIds = importPOList.stream()
            .filter(item -> Objects.equals(XpdImportTypeEnum.DIM.getCode(), item.getImportType()))
            .map(XpdImportPO::getSdDimId).collect(Collectors.toList());
        List<XpdDimRuleDto> dimRulePool = BeanCopierUtil.convertList(xpdDimRuleMapper.listByXpdId(orgId, xpdId), dimRule -> {
            XpdDimRuleDto ruleDto = new XpdDimRuleDto();
            BeanCopierUtil.copy(dimRule, ruleDto);
            ruleDto.setFormulaCalc(FormulaTypeEnum.parseExpression(dimRule.getFormula()));
            ruleDto.setGridLevelRules(CommonUtil.tryParseArray(dimRule.getLevelRule(), DimGridLevelRuleDTO.class));
            ruleDto.setRuleThresholdDto(CommonUtils.tryParseObject(dimRule.getRuleThreshold(), RatioLevelThresholdDto.class));
            return ruleDto;
        });
        List<XpdDimRuleCalcDto> ruleCalcList = BeanCopierUtil.convertList(xpdDimRuleCalcMapper.listByXpdId(orgId, xpdId), ruleCalc -> {
            XpdDimRuleCalcDto calcDto = new XpdDimRuleCalcDto();
            BeanCopierUtil.copy(ruleCalc, calcDto);
            calcDto.setRefList(CommonUtil.tryParseArray(ruleCalc.getRefIds(), XpdDimRuleCalcRefDto.class));
            return calcDto;
        });
        Map<String, List<XpdDimRuleCalcDto>>
                ruleCalcMap = IArrayUtils.list2Map(ruleCalcList, XpdDimRuleCalcDto::getDimRuleId);
        List<DimRuleMetaDTO> calcRuleList = new ArrayList<>();
        for (XpdDimPO xpdDimPO : xpdDimList) {
            XpdDimRuleDto dimRuleDto = IArrayUtils.getFirstMatch(dimRulePool,
                    dimRule -> Objects.equals(dimRule.getSdDimId(), xpdDimPO.getSdDimId()));
            DimRuleMetaDTO dimRuleMeta = new DimRuleMetaDTO();
            dimRuleMeta.xpdDimId = xpdDimPO.getId();
            dimRuleMeta.sdDimId = xpdDimPO.getSdDimId();
            dimRuleMeta.savedScoreTotal = xpdDimPO.getScoreTotal();
            dimRuleMeta.coverByImport = importSdDimIds.contains(xpdDimPO.getSdDimId());
            if (dimRuleDto != null) {
                dimRulePool.remove(dimRuleDto);
                dimRuleMeta.dimRuleBase = dimRuleDto;
                dimRuleMeta.subDimRules = buildSubDimRule(dimRuleDto.getId(), dimRulePool, ruleCalcMap);
                dimRuleMeta.ruleCalcList = ruleCalcMap.get(dimRuleDto.getId());
            }
            calcRuleList.add(dimRuleMeta);
        }
        return calcRuleList;
    }

    public CaliDimResultResp queryCaliResult(String orgId, String caliMeetId, String userId) {
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        if (calimeet == null) {
            return null;
        }
        CalimeetBriefDto briefDto = new CalimeetBriefDto();
        BeanCopierUtil.copy(calimeet, briefDto);
        return doQueryCaliResult(briefDto, userId, calimeetRecordMapper.getLastDetail(orgId, caliMeetId, userId));
    }

    private void fillCaliResultItems(XpdCaliCalcFullDto fullDto, CaliDimResultResp resp, String caliMeetId, int caliType,
        CalimeetDimResultDto resultDto, List<CalimeetResultUserDimPO> caliDumpDimResults) {
        String orgId = fullDto.getOrgId();
        String userId = resultDto.getUserId();
        boolean getScore = fullDto.getResultType() == DimResultTypeEnum.SCORE_VALUE.getCode();
        if (caliType == CaliMeetTypeEnum.LEVEL.getCode()) {
            if (resultDto.getCaliDetailWrapDto() == null) {
                resultDto.setCaliDetailWrapDto(CommonUtils.tryParseObject(resultDto.getCaliDetails(), CaliUpdateUserResultWrapDto.class));
            }
            Map<String, CaliDimResultItemValDto> xpdDimResultMap = IArrayUtils.listAsMap(caliDumpDimResults,
                CalimeetResultUserDimPO::getSdDimId, this::convertToXpdItemVal);
            Map<String, CaliUpdateUserResultDto> savedDimResultMap = IArrayUtils.listAsMap(Optional.ofNullable(resultDto.getCaliDetailWrapDto())
                .map(CaliUpdateUserResultWrapDto::getUserResults).orElse(null), CaliUpdateUserResultDto::getSdDimId, item -> item);
            resp.setGridLevelList(fullDto.getGridLevelList());
            resp.setResultType(CaliResultTypeEnum.TYPE_DIM_LEVEL.getType());
            resp.setItems(BeanCopierUtil.convertList(fullDto.getRuleList(), rule -> {
                CaliDimResultItemDto itemDto = new CaliDimResultItemDto();
                itemDto.setSdDimId(rule.getSdDimId());
                itemDto.setItemName(fullDto.getSdDimNameMap().get(rule.getSdDimId()));
                itemDto.setXpdValBean(xpdDimResultMap.get(rule.getSdDimId()));
                itemDto.setCaliValBean(Optional.ofNullable(savedDimResultMap.get(rule.getSdDimId())).map(dimResult -> {
                    CaliDimResultItemValDto caliVal = new CaliDimResultItemValDto();
                    BeanCopierUtil.copy(dimResult, caliVal);
                    return caliVal;
                }).orElse(null));
                caliDimResultItemSetVal(itemDto, CaliDimResultItemValDto::getGridLevelId, String::toString);
                return itemDto;
            }));
        } else if (caliType == CaliMeetTypeEnum.DIM.getCode()) {
            Map<String, CaliDimResultItemValDto> xpdDimResultMap = IArrayUtils.listAsMap(caliDumpDimResults,
                CalimeetResultUserDimPO::getSdDimId, this::convertToXpdItemVal);
            Map<String, CaliUpdateUserResultDto> savedDimResultMap = IArrayUtils.listAsMap(Optional.ofNullable(CommonUtils.tryParseObject(resultDto.getCaliDetails(), CaliUpdateUserResultWrapDto.class))
                .map(CaliUpdateUserResultWrapDto::getUserResults).orElse(null), CaliUpdateUserResultDto::getSdDimId, item -> item);
            resp.setResultType(getScore ? CaliResultTypeEnum.TYPE_DIM_SCORE.getType() : CaliResultTypeEnum.TYPE_QUALIFIED_PTG.getType());
            resp.setItems(fullDto.getRuleList().stream()
                .filter(dimRule -> dimRule.getCaliCalcType() != XpdDimRule4Cali.TYPE_NONE
                                   && !Objects.equals(dimRule.getCalcType(), DimCalcTypeEnum.PERF_RESULT.getCode())
                                   && !(!getScore && Objects.equals(dimRule.getCalcType(), DimCalcTypeEnum.PERF_SCORE.getCode())))
                .map(rule -> {
                    CaliDimResultItemDto itemDto = new CaliDimResultItemDto();
                    itemDto.setSdDimId(rule.getSdDimId());
                    itemDto.setItemName(fullDto.getSdDimNameMap().get(rule.getSdDimId()));
                    itemDto.setXpdValBean(xpdDimResultMap.get(rule.getSdDimId()));
                    itemDto.setCaliValBean(Optional.ofNullable(savedDimResultMap.get(rule.getSdDimId())).map(dimResult -> {
                        CaliDimResultItemValDto caliVal = new CaliDimResultItemValDto();
                        BeanCopierUtil.copy(dimResult, caliVal);
                        return caliVal;
                    }).orElse(null));
                    itemDto.setTotalScore(rule.getTotalScore());
                    caliDimResultItemSetVal(itemDto,
                        getScore ? CaliDimResultItemValDto::getScoreValue : CaliDimResultItemValDto::getQualifiedPtg,
                        BigDecimal::toPlainString);
                    return itemDto;
                }).collect(Collectors.toList()));
        } else {
            Set<String> indicatorIdSet = new HashSet<>();
            List<XpdDimRule4Cali> useDims = new ArrayList<>();
            fullDto.getRuleList().forEach(rule -> {
                if (rule.getCaliCalcType() != XpdDimRule4Cali.TYPE_NONE
                    && !DimCalcTypeEnum.isPerf(rule.getCalcType())
                    && rule.getFormulaSdIndicatorSet() != null) {
                    indicatorIdSet.addAll(rule.getFormulaSdIndicatorSet());
                    useDims.add(rule);
                }
            });
            Map<String, CaliDimResultItemValDto> xpdIndResultMap = IArrayUtils.listAsMap(calimeetResultUserIndicatorMapper.getByUserIdIndicatorIds(orgId, caliMeetId, Lists.newArrayList(userId), indicatorIdSet),
                CalimeetResultUserIndicatorPO::getSdIndicatorId, this::convertToXpdItemVal);
            Map<String, CaliUpdateUserResultDto> savedIndResultMap = IArrayUtils.listAsMap(Optional.ofNullable(CommonUtils.tryParseObject(resultDto.getCaliDetails(), CaliUpdateUserResultWrapDto.class))
                .map(CaliUpdateUserResultWrapDto::getUserResults).orElse(null), CaliUpdateUserResultDto::getSdIndicatorId, item -> item);
            resp.setResultType(getScore ? CaliResultTypeEnum.TYPE_INDICATOR_SCORE.getType() : CaliResultTypeEnum.TYPE_QUALIFIED.getType());
            resp.setItems(indicatorIdSet.stream().map(sdIndicatorId -> {
                String sdDimId = IArrayUtils.getFirstMatch(useDims, dim -> dim.getFormulaSdIndicatorSet().contains(sdIndicatorId)).getSdDimId();
                CaliDimResultItemDto itemDto = new CaliDimResultItemDto();
                itemDto.setSdDimId(sdDimId);
                itemDto.setSdDimName(fullDto.getSdDimNameMap().get(sdDimId));
                itemDto.setSdIndicatorId(sdIndicatorId);
                XpdSdIndicatorScoreDto scoreDto = fullDto.getSdIndicatorMap().get(sdIndicatorId);
                if (scoreDto != null) {
                    itemDto.setItemName(scoreDto.getName());
                    itemDto.setTotalScore(scoreDto.getTotalScore());
                }
                itemDto.setXpdValBean(xpdIndResultMap.get(sdIndicatorId));
                itemDto.setCaliValBean(Optional.ofNullable(savedIndResultMap.get(sdIndicatorId)).map(dimResult -> {
                    CaliDimResultItemValDto caliVal = new CaliDimResultItemValDto();
                    BeanCopierUtil.copy(dimResult, caliVal);
                    return caliVal;
                }).orElse(null));
                if (getScore) {
                    caliDimResultItemSetVal(itemDto, CaliDimResultItemValDto::getScoreValue, BigDecimal::toPlainString);
                } else {
                    caliDimResultItemSetVal(itemDto, CaliDimResultItemValDto::getQualified, String::valueOf);
                }
                return itemDto;
            }).collect(Collectors.toList()));
        }
    }

    public CaliDimResultResp doQueryCaliResult(CalimeetBriefDto calimeet, String userId, CalimeetDimResultDto resultDto) {
        String orgId = calimeet.getOrgId();
        String caliMeetId = calimeet.getId();
        //维度分层结果，维度得分或者达标率，指标得分/是否达标
        int caliType = calimeet.getCalimeetType();
        String xpdId = calimeet.getXpdId();
        XpdCaliCalcFullDto fullDto = buildXpdCalcFormula(orgId, xpdId);
        if (fullDto == null) {
            return null;
        }
        CaliDimResultResp resp = new CaliDimResultResp();
        boolean firstCali = resultDto == null;
        resultDto = Optional.ofNullable(resultDto).orElse(new CalimeetDimResultDto());
        resultDto.setUserId(userId);
        resp.setSuggestion(resultDto.getSuggestion());
        resp.setReason(resultDto.getReason());
        List<CalimeetResultUserDimPO> caliDumpDimResults = calimeetResultUserDimMapper.getByUserIdDimIds(orgId, caliMeetId, Lists.newArrayList(userId),
            BeanCopierUtil.convertList(fullDto.getRuleList(), XpdDimRule4Cali::getSdDimId));
        fillCaliResultItems(fullDto, resp, caliMeetId, caliType, resultDto, caliDumpDimResults);
        CaliDimResultWrapDto dimResults = CommonUtils.tryParseObject(resultDto.getResultDetails(), CaliDimResultWrapDto.class);
        Map<String, CaliDimResultBean> dimResultMap = IArrayUtils.listAsMap(Optional.ofNullable(dimResults).map(CaliDimResultWrapDto::getDimResults).orElse(null),
            CaliDimResultDto::getSdDimId, caliDimResult -> {
                CaliDimResultBean dimResultBean = new CaliDimResultBean();
                dimResultBean.setSdDimId(caliDimResult.getSdDimId());
                dimResultBean.setGridLevelId(caliDimResult.getGridLevelId());
                return dimResultBean;
            });
        caliDumpDimResults.forEach(xpdDimResult -> {
            CaliDimResultBean dimResultBean = dimResultMap.get(xpdDimResult.getSdDimId());
            if (dimResultBean == null) {
                dimResultBean = new CaliDimResultBean();
                if (firstCali) {
                    dimResultBean.setGridLevelId(xpdDimResult.getGridLevelId());
                    dimResultBean.setScoreValue(xpdDimResult.getScoreValue());
                    dimResultBean.setQualifiedPtg(xpdDimResult.getQualifiedPtg());
                }
                dimResultMap.put(xpdDimResult.getSdDimId(), dimResultBean);
            }
            dimResultBean.setXpdGridLevelId(xpdDimResult.getGridLevelId());
            dimResultBean.setXpdScoreValue(xpdDimResult.getScoreValue());
            dimResultBean.setXpdQualifiedPtg(xpdDimResult.getQualifiedPtg());
        });
        resp.setCombList(assembleDimCombResult(fullDto, dimResultMap));
        resp.setXpdLevelId(calimeetResultUserMapper.userLevelId(orgId, caliMeetId, userId));
        resp.setXpdLevelName(IArrayUtils.getFirstMatch(fullDto.getXpdRuleLevels(),
            ruleLevel -> ruleLevel.getId().equals(resp.getXpdLevelId()),
            XpdRuleLevelDto::getLevelName));
        if (firstCali) {
            resp.setCaliLevelId(resp.getXpdLevelId());
            resp.setCaliLevelName(resp.getXpdLevelName());
        } else {
            resp.setCaliLevelId(Optional.ofNullable(dimResults).map(CaliDimResultWrapDto::getXpdLevelId).orElse(null));
            resp.setCaliLevelName(IArrayUtils.getFirstMatch(fullDto.getXpdRuleLevels(),
                ruleLevel -> ruleLevel.getId().equals(resp.getCaliLevelId()),
                XpdRuleLevelDto::getLevelName));
        }
        return resp;
    }

    private <T> void caliDimResultItemSetVal(CaliDimResultItemDto itemDto, Function<CaliDimResultItemValDto, T> valGetter, Function<T, String> valToStr) {
        if (itemDto.getXpdValBean() != null) {
            T value = valGetter.apply(itemDto.getXpdValBean());
            if (value != null) {
                itemDto.setXpdVal(valToStr.apply(value));
            }
        }
        if (itemDto.getCaliValBean() != null) {
            T value = valGetter.apply(itemDto.getCaliValBean());
            if (value != null) {
                itemDto.setCaliVal(valToStr.apply(value));
            }
        }
    }

    private CaliDimResultItemValDto convertToXpdItemVal(CalimeetResultUserDimPO xpdDimResult) {
        CaliDimResultItemValDto itemVal = new CaliDimResultItemValDto();
        itemVal.setGridLevelId(xpdDimResult.getGridLevelId());
        itemVal.setScoreValue(xpdDimResult.getScoreValue());
        itemVal.setQualifiedPtg(xpdDimResult.getQualifiedPtg());
        itemVal.setPerfLevelId(xpdDimResult.getPerfResultId());
        return itemVal;
    }

    private CaliDimResultItemValDto convertToXpdItemVal(CalimeetResultUserIndicatorPO xpdIndResult) {
        CaliDimResultItemValDto itemVal = new CaliDimResultItemValDto();
        itemVal.setScoreValue(xpdIndResult.getScoreValue());
        itemVal.setQualified(xpdIndResult.getQualified());
        itemVal.setPerfLevelId(xpdIndResult.getPerfResultId());
        return itemVal;
    }

    public void batchCalcCaliResult(String orgId, String optUserId, String caliMeetId, List<CaliUserResultContainer> inputList) {
        CalcCaliResultContext caliCalcCtx = prepareCalcCali(orgId, caliMeetId);
        if (caliCalcCtx == null) {
            return;
        }
        BatchOperationUtil.batchExecute(inputList, 50, list -> {
            loadCalcCaliUserData(caliCalcCtx, list);
            List<CaliUserIdDto> caliUserIds = calimeetUserMapper.listIdByUserIds(orgId, caliMeetId,
                BeanCopierUtil.convertList(list, CaliUserResultContainer::getUserId));
            Map<String, CaliUserIdDto> userIdMap = StreamUtil.list2map(caliUserIds, CaliUserIdDto::getUserId);
            List<CalimeetRecordPO> calimeetRecords = new ArrayList<>(list.size());
            List<CalimeetRecordItemPO> recordItems = new ArrayList<>(list.size() * 2);
            list.forEach(item -> {
                doCalcCaliResult(caliCalcCtx, item);
                CaliDimResultResp calcResult = item.getCalcResult();
                CalimeetRecordPO recordPO = new CalimeetRecordPO();
                recordPO.setOrgId(orgId);
                recordPO.setCalimeetId(caliMeetId);
                recordPO.setUserId(item.getUserId());
                recordPO.setSuggestion(item.getSuggestion());
                recordPO.setReason(item.getReason());
                CaliUpdateUserResultWrapDto resultWrapDto = new CaliUpdateUserResultWrapDto();
                resultWrapDto.setResultType(calcResult.getResultType());
                resultWrapDto.setUserResults(BeanCopierUtil.convertList(item.getResultList(),
                    CaliUpdateUserResultReq.class,
                    CaliUpdateUserResultDto.class));
                recordPO.setCaliDetails(JSON.toJSONString(resultWrapDto));
                CaliDimResultWrapDto calcResultDto = new CaliDimResultWrapDto();
                calcResultDto.setXpdLevelId(item.getCalcResult().getCaliLevelId());
                calcResultDto.setScoreValue(calcResult.getCaliScore());
                calcResultDto.setQualifiedPtg(calcResult.getCaliQualifiedPtg());
                calcResultDto.setDimResults(Lists.newArrayList());
                item.getCalcResult().getDimResultMap().forEach((sdDimId, dimResult) -> {
                    if (!dimResult.isNoneResult()) {
                        CaliDimResultDto resultDto = new CaliDimResultDto();
                        BeanCopierUtil.copy(dimResult, resultDto);
                        calcResultDto.getDimResults().add(resultDto);
                    }
                });
                recordPO.setResultDetails(JSON.toJSONString(calcResultDto));
                recordPO.initData(optUserId);
                calimeetRecords.add(recordPO);
                Optional.ofNullable(userIdMap.get(recordPO.getUserId())).ifPresent(userIdDto -> userIdDto.setLatestRecordId(recordPO.getId()));
                item.getCalcResult().getCombList().forEach(dimComb -> {
                    if (!dimComb.isAffectComb()) {
                        //没有影响到的维度组合就不保存了
                        return;
                    }
                    CalimeetRecordItemPO recordItem = new CalimeetRecordItemPO();
                    recordItem.setOrgId(orgId);
                    recordItem.setCalimeetId(caliMeetId);
                    recordItem.setUserId(item.getUserId());
                    recordItem.setCalimeetRecordId(recordPO.getId());
                    recordItem.setDimCombId(dimComb.getCombId());
                    recordItem.setOriginalCellIndex(dimComb.getXpdCellIndex());
                    recordItem.setCellIndex(dimComb.getCaliCellIndex());
                    recordItem.setCaliShift(dimComb.getCaliGap());
                    recordItem.initData(optUserId);
                    recordItems.add(recordItem);
                });
            });
            BatchOperationUtil.batchSave(calimeetRecords, calimeetRecordMapper::batchInsert);
            BatchOperationUtil.batchSave(recordItems, calimeetRecordItemMapper::batchInsert);
            BatchOperationUtil.batchSave(caliUserIds, calimeetUserMapper::batchUpdateLatestRecord);
        });
    }

    public CaliDimResultResp viewCalcCaliResult(String orgId, String caliMeetId, String userId, List<CaliUpdateUserResultReq> resultList) {
        return viewCalcCaliResult(orgId, caliMeetId, userId, resultList, Pair.of(false, null));
    }

    public CaliDimResultResp viewCalcCaliResult(String orgId, String caliMeetId, String userId,
        List<CaliUpdateUserResultReq> resultList, Pair<Boolean, CalimeetDimResultDto> dimResultPair) {
        CalcCaliResultContext caliCalcCtx = prepareCalcCali(orgId, caliMeetId);
        if (caliCalcCtx == null) {
            return null;
        }
        CaliUserResultContainer container = new CaliUserResultContainer();
        container.setUserId(userId);
        container.setResultList(resultList);
        container.setDimResultPair(dimResultPair);
        loadCalcCaliUserData(caliCalcCtx, Lists.newArrayList(container));
        doCalcCaliResult(caliCalcCtx, container);
        return container.getCalcResult();
    }
    private class CalcCaliResultContext {
        private String orgId;
        private String xpdId;
        private String caliMeetId;
        private int caliType;
        private XpdCaliCalcFullDto fullDto;
        private Map<String, XpdDimRule4Cali> dimRuleMap;
        private RuleMainBase xpdRuleMain;
        private Map<Integer, Pair<Set<String>, Set<String>>> refMapRefIds;
    }

    private CalcCaliResultContext prepareCalcCali(String orgId, String caliMeetId) {
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        if (calimeet == null) {
            return null;
        }
        //维度分层结果，维度得分或者达标率，指标得分/是否达标
        int caliType = calimeet.getCalimeetType();
        String xpdId = calimeet.getXpdId();
        XpdCaliCalcFullDto fullDto = buildXpdCalcFormula(orgId, xpdId);
        if (fullDto == null) {
            return null;
        }
        CalcCaliResultContext calcCaliCtx = new CalcCaliResultContext();
        calcCaliCtx.orgId = orgId;
        calcCaliCtx.xpdId = xpdId;
        calcCaliCtx.caliMeetId = caliMeetId;
        calcCaliCtx.caliType = caliType;
        calcCaliCtx.fullDto = fullDto;
        calcCaliCtx.xpdRuleMain = new RuleMainBase();
        calcCaliCtx.xpdRuleMain.setOrgId(orgId);
        calcCaliCtx.xpdRuleMain.setBizId(xpdId);
        calcCaliCtx.dimRuleMap = StreamUtil.list2map(fullDto.getRuleList(), XpdDimRule4Cali::getSdDimId);

        //Pair<refIds,indicatorIds>
        Map<Integer, Pair<Set<String>, Set<String>>> refMapRefIds = new HashMap<>();
        fullDto.getAllFormulaRefIndicators().forEach(refInds -> {
            Pair<Set<String>, Set<String>> pair = refMapRefIds.computeIfAbsent(refInds.getRefType(), refTypeKey -> Pair.of(new HashSet(), new HashSet()));
            pair.getKey().add(refInds.getRefId());
            pair.getValue().addAll(refInds.getIndicatorIds());
        });
        calcCaliCtx.refMapRefIds = refMapRefIds;
        return calcCaliCtx;
    }

    private void loadCalcCaliUserData(CalcCaliResultContext caliCalcCtx, List<CaliUserResultContainer> list) {
        Map<String, CaliUserResultContainer> userMap = StreamUtil.list2map(list, CaliUserResultContainer::getUserId);
        Set<String> userIds = userMap.keySet();
        String orgId = caliCalcCtx.orgId;
        String xpdId = caliCalcCtx.xpdId;
        String caliMeetId = caliCalcCtx.caliMeetId;
        XpdCaliCalcFullDto fullDto = caliCalcCtx.fullDto;
        Set<String> sdDimIds = caliCalcCtx.dimRuleMap.keySet();
        caliCalcCtx.refMapRefIds.forEach((refType, refIdsAndIndIds) -> {
            Set<String> refIds = refIdsAndIndIds.getKey();
            Set<String> indicatorIds = refIdsAndIndIds.getValue();
            List<XpdIndicatorResultDto> refResultList = null;
            if (refType == DimRuleCalcRefEnum.AOM_ACT.getCode()) {
                baseActivityResultRepo.doneRefIds(orgId,
                        fullDto.getAomPrjId(), userIds, refIds)
                    .forEach(userRefId -> userMap.get(userRefId.getUserId()).getAomDoneRefIds().add(userRefId.getRefId()));
                //查询已完成的活动结果
                refResultList = activityObjectiveResultRepo.queryByActvIds(orgId, userIds, refIds, indicatorIds);
            } else if (refType == DimRuleCalcRefEnum.IMPORT_DATA.getCode()) {
                refResultList = xpdImportIndicatorUserRepo.queryByImportIds(orgId, xpdId, userIds, refIds, indicatorIds);
            } else if (refType == DimRuleCalcRefEnum.PRI_PROFILE.getCode()) {
                refResultList = dwdUserIndicatorResultRepo.listUserIndicator(orgId, userIds, indicatorIds);
            }
            if (refResultList != null) {
                refResultList.forEach(indResult -> {
                    CaliUserResultContainer container = userMap.get(indResult.getUserId());
                    container.getRefIndResultMap().computeIfAbsent(refType, key -> new ArrayList<>()).add(indResult);
                });
            }
        });
        if (!fullDto.getSdIndicatorMap().isEmpty()) {
            calimeetResultUserIndicatorMapper.getByUserIdIndicatorIds(orgId, caliMeetId, userIds, fullDto.getSdIndicatorMap().keySet())
                .forEach(userIndResult -> userMap.get(userIndResult.getUserId()).getXpdIndicatorResults().add(userIndResult));
        }
        if (!sdDimIds.isEmpty()) {
            calimeetResultUserDimMapper.getByUserIdDimIds(orgId, caliMeetId, userIds, sdDimIds)
                .forEach(xpdDimResult -> userMap.get(xpdDimResult.getUserId()).getXpdDimResults().add(xpdDimResult));
        }
    }

    private void doCalcCaliResult(CalcCaliResultContext caliCalcCtx, CaliUserResultContainer resultContainer) {
        String orgId = caliCalcCtx.orgId;
        String caliMeetId = caliCalcCtx.caliMeetId;
        //维度分层结果，维度得分或者达标率，指标得分/是否达标
        int caliType = caliCalcCtx.caliType;
        String xpdId = caliCalcCtx.xpdId;
        XpdCaliCalcFullDto fullDto = caliCalcCtx.fullDto;
        CaliDimResultResp resp = new CaliDimResultResp();
        boolean getScore = fullDto.getResultType() == DimResultTypeEnum.SCORE_VALUE.getCode();
        Map<String, XpdDimRule4Cali> dimRuleMap = caliCalcCtx.dimRuleMap;
        List<CaliUpdateUserResultReq> resultList = resultContainer.getResultList();
        String userId = resultContainer.getUserId();
        //结果集
        CaliUserResults userResults = new CaliUserResults();
        userResults.aomDoneRefIds = resultContainer.getAomDoneRefIds();
        caliCalcCtx.refMapRefIds.forEach((refType, refIdsAndIndIds) -> {
            List<XpdIndicatorResultDto> refResultList = resultContainer.getRefIndResultMap().get(refType);
            if (refResultList != null) {
                refResultList.forEach(indResult -> {
                    DimCalcUserResultDto userResult = new DimCalcUserResultDto();
                    userResult.setUserId(indResult.getUserId());
                    userResult.setScoreValue(indResult.getScore());
                    Optional.ofNullable(CommonUtils.tryParseObject(indResult.getExtJson(), ActvResultExtDto.class))
                        .map(ActvResultExtDto::getEvalDimScore).ifPresent(userResult::setEvalScoreMap);
                    String mainKey = mainKey(refType, indResult.getRefId(), indResult.getSdIndicatorId());
                    userResults.refResultMap.put(mainKey, userResult);
                });
            }
        });
        userResults.directScoreMap = new HashMap<>();
        resultList.stream().filter(item -> item.getSdIndicatorId() != null && item.getScoreValue() != null)
            .forEach(item -> userResults.directScoreMap.put(item.getSdIndicatorId(), item.getScoreValue()));
        userResults.indResultMap = new HashMap<>();
        userResults.dimResultMap = new HashMap<>();
        resultContainer.getXpdIndicatorResults().forEach(userIndResult -> {
            CaliUpdateUserResultReq userResultDto = new CaliUpdateUserResultReq();
            userResultDto.setQualified(userIndResult.getQualified());
            userResultDto.setScoreValue(userIndResult.getScoreValue());
            userResultDto.setPerfLevelId(userIndResult.getPerfResultId());
            userResults.indResultMap.put(userIndResult.getSdIndicatorId(), userResultDto);
        });
        boolean fillBeforeResult = Optional.ofNullable(resultContainer.getDimResultPair())
            .map(Pair::getKey).orElse(false);
        if (fillBeforeResult) {
            CalimeetDimResultDto dimResultDto = Optional.ofNullable(resultContainer.getDimResultPair().getValue())
                    .orElse(new CalimeetDimResultDto());
            dimResultDto.setUserId(userId);
            dimResultDto.setCaliDetailWrapDto(new CaliUpdateUserResultWrapDto());
            dimResultDto.getCaliDetailWrapDto().setUserResults(BeanCopierUtil.convertList(resultList, CaliUpdateUserResultReq.class, CaliUpdateUserResultDto.class));
            fillCaliResultItems(fullDto, resp, caliMeetId, caliType, dimResultDto, resultContainer.getXpdDimResults());
            resp.setReason(dimResultDto.getReason());
            resp.setSuggestion(dimResultDto.getSuggestion());
        }
        if (caliType == CaliMeetTypeEnum.LEVEL.getCode()) {
            resp.setResultType(CaliResultTypeEnum.TYPE_DIM_LEVEL.getType());
            //维度分层结果
            resultList.forEach(resultDto -> {
                CaliDimResultBean dimResult = new CaliDimResultBean();
                dimResult.setUserId(userId);
                dimResult.setSdDimId(resultDto.getSdDimId());
                dimResult.setGridLevelId(resultDto.getGridLevelId());
                dimResult.setAffectDim(true);
                userResults.dimResultMap.put(dimResult.getSdDimId(), dimResult);
            });
        } else if (caliType == CaliMeetTypeEnum.DIM.getCode()) {
            resp.setResultType(getScore ? CaliResultTypeEnum.TYPE_DIM_SCORE.getType() : CaliResultTypeEnum.TYPE_QUALIFIED_PTG.getType());
            //维度得分或者达标率
            for (CaliUpdateUserResultReq resultDto : resultList) {
                String sdDimId = resultDto.getSdDimId();
                XpdDimRule4Cali dimRule = dimRuleMap.get(sdDimId);
                if (dimRule == null || dimRule.getCaliCalcType() == XpdDimRule4Cali.TYPE_NONE) {
                    continue;
                }
                if (!Objects.equals(dimRule.getCalcType(), DimCalcTypeEnum.PERF_RESULT.getCode())) {
                    XpdCaliCalcDimParam param = new XpdCaliCalcDimParam();
                    param.setOrgId(orgId);
                    param.setXpdId(xpdId);
                    param.setByScore(getScore);
                    param.setSdDimId(sdDimId);
                    BigDecimal getValue = Optional.ofNullable(getScore ? resultDto.getScoreValue() : resultDto.getQualifiedPtg()).orElse(BigDecimal.ZERO);
                    calcCaliDimResult(param, dimRule, userId, getValue,
                        dimResult -> userResults.dimResultMap.put(dimResult.getSdDimId(), dimResult));
                }
            }
        } else {
            resp.setResultType(getScore ? CaliResultTypeEnum.TYPE_INDICATOR_SCORE.getType() : CaliResultTypeEnum.TYPE_QUALIFIED.getType());
            //指标得分/是否达标
            Set<String> effectSdDimIds = new HashSet<>();
            for (CaliUpdateUserResultReq resultDto : resultList) {
                String sdIndicatorId = resultDto.getSdIndicatorId();
                fullDto.getRuleList().forEach(dimRule -> {
                    if (dimRule.getCaliCalcType() != XpdDimRule4Cali.TYPE_NONE
                        && !DimCalcTypeEnum.isPerf(dimRule.getCalcType())
                        && Optional.ofNullable(dimRule.getFormulaSdIndicatorSet())
                            .orElse(new HashSet<>()).contains(sdIndicatorId)) {
                        effectSdDimIds.add(dimRule.getSdDimId());
                    }
                });
            }
            //覆盖数据库的值
            resultList.forEach(resultDto -> userResults.indResultMap.put(resultDto.getSdIndicatorId(), resultDto));
            for (String effectSdDimId : effectSdDimIds) {
                XpdDimRule4Cali dimRule = dimRuleMap.get(effectSdDimId);
                if (Objects.equals(dimRule.getCalcType(), DimCalcTypeEnum.PERF_RESULT.getCode())) {
                    CaliUpdateUserResultReq resultDto = userResults.indResultMap.get(dimRule.getPerfSdIndicatorId());
                    if (resultDto == null) {
                        continue;
                    }
                    //绩效维度按结果
                    DimGridLevelRuleDTO matchLevel = IArrayUtils.getFirstMatch(dimRule.getGridLevelRules(), levelRule -> {
                        if (CollectionUtils.isNotEmpty(levelRule.getMatchValues()) && StringUtils.isNotEmpty(resultDto.getPerfLevelId())) {
                            return levelRule.getMatchValues().contains(resultDto.getPerfLevelId());
                        }
                        return false;
                    });
                    if (matchLevel != null) {
                        CaliDimResultBean dimResult = new CaliDimResultBean();
                        dimResult.setUserId(userId);
                        dimResult.setSdDimId(dimRule.getSdDimId());
                        dimResult.setGridLevelId(matchLevel.getGridLevelId());
                        userResults.dimResultMap.put(dimResult.getSdDimId(), dimResult);
                    } else {
                        CaliDimResultBean dimResult = new CaliDimResultBean();
                        dimResult.setNoneResult(true);
                        dimResult.setUserId(userId);
                        dimResult.setSdDimId(dimRule.getSdDimId());
                        userResults.dimResultMap.put(dimResult.getSdDimId(), dimResult);
                    }
                } else {
                    BigDecimal scoreVal = null;
                    if (Objects.equals(dimRule.getCalcType(), DimCalcTypeEnum.PERF_SCORE.getCode())) {
                        CaliUpdateUserResultReq resultDto = userResults.indResultMap.get(dimRule.getPerfSdIndicatorId());
                        scoreVal = Optional.ofNullable(resultDto).map(CaliUpdateUserResultReq::getScoreValue).orElse(null);
                    } else if (StringUtils.isNotEmpty(dimRule.getCalcFormula())) {
                        if (Objects.equals(dimRule.getCaliCalcType(), XpdDimRule4Cali.TYPE_BUILD_FORMULA)) {
                            Double resultVal = null;
                            try {
                                resultVal = new ExpressionCalc(dimRule.getCalcFormula())
                                    .calcResult(chip -> {
                                        String sdIndicatorId = IArrayUtils.getFirst(chip.getParamList());
                                        CaliUpdateUserResultReq resultDto = userResults.indResultMap.get(sdIndicatorId);
                                        if (resultDto == null) {
                                            return null;
                                        }
                                        if (getScore) {
                                            return Optional.ofNullable(resultDto.getScoreValue()).orElse(BigDecimal.ZERO);
                                        } else {
                                            return Optional.ofNullable(resultDto.getQualified()).orElse(0);
                                        }
                                    });
                            } catch (Exception e) {
                                log.warn("calcCaliIndicatorFormulaResult orgId {} xpdId {} effectSdDimId {} failed {}", orgId, xpdId, effectSdDimId, e.getMessage());
                            }
                            if (resultVal != null && Double.isFinite(resultVal)) {
                                scoreVal = CommonUtil.valueOf(resultVal, MC_TWO);
                                if (!getScore && scoreVal.compareTo(AppConstants.HUNDRED) > 0) {
                                    //保证得分率最大100
                                    scoreVal = AppConstants.HUNDRED;
                                }
                            }
                        } else if (Objects.equals(dimRule.getCaliCalcType(), XpdDimRule4Cali.TYPE_FORMULA)
                                   && getScore) {
                            ExpressionCalc formulaCalc = FormulaTypeEnum.parseExpression(dimRule.getCalcFormula());
                            try {
                                Double result = formulaCalc.calcResult(paramChip -> {
                                    if (paramChip.getParamBean() == null) {
                                        return BigDecimal.ZERO;
                                    }
                                    return formulaChipUserScore(fullDto.getFastFormulaIndicatorMap(), userResults, paramChip);
                                });
                                if (result != null && Double.isFinite(result)) {
                                    scoreVal = CommonUtil.valueOf(result, MC_TWO);
                                    scoreVal = convertToTargetSystem(scoreVal, dimRule.getCalcTotalScore(), fullDto.getScoreSystem());
                                }
                            } catch (Exception e) {
                                log.warn("calcCaliFormulaResult orgId {} xpdId {} effectSdDimId {} failed {}", orgId, xpdId, effectSdDimId, e.getMessage());
                            }
                        }
                    }
                    log.info("calcCaliResultByIndicator sdDimId {} scoreVal {}", effectSdDimId, scoreVal);
                    XpdCaliCalcDimParam param = new XpdCaliCalcDimParam();
                    param.setOrgId(orgId);
                    param.setXpdId(xpdId);
                    param.setByScore(getScore);
                    param.setSdDimId(dimRule.getSdDimId());
                    calcCaliDimResult(param, dimRule, userId, scoreVal,
                        dimResult -> userResults.dimResultMap.put(dimResult.getSdDimId(), dimResult));
                }
            }
        }
        //查询盘点维度结果
        resultContainer.getXpdDimResults().forEach(xpdDimResult -> {
            CaliDimResultBean dimResultBean = userResults.dimResultMap.get(xpdDimResult.getSdDimId());
            if (dimResultBean == null) {
                //如果没有，则没参与计算用原始结果
                dimResultBean = new CaliDimResultBean();
                dimResultBean.setUserId(userId);
                dimResultBean.setSdDimId(xpdDimResult.getSdDimId());
                dimResultBean.setGridLevelId(xpdDimResult.getGridLevelId());
                dimResultBean.setScoreValue(xpdDimResult.getScoreValue());
                dimResultBean.setQualifiedPtg(xpdDimResult.getQualifiedPtg());
                userResults.dimResultMap.put(xpdDimResult.getSdDimId(), dimResultBean);
            }
            dimResultBean.setXpdGridLevelId(xpdDimResult.getGridLevelId());
            dimResultBean.setXpdScoreValue(xpdDimResult.getScoreValue());
            dimResultBean.setXpdQualifiedPtg(xpdDimResult.getQualifiedPtg());
        });
        resp.setDimResultMap(userResults.dimResultMap);
        resp.setCombList(assembleDimCombResult(fullDto, userResults.dimResultMap));
        XpdRule4CaliDto xpdRuleDto = fullDto.getXpdRuleDto();
        XpdRuleLevelDto matchXpdLevel = null;
        if (xpdRuleDto != null) {
            boolean byIndicator = Objects.equals(XpdCalcTypeEnum.BY_INDICATOR.getCode(), xpdRuleDto.getCalcType());
            boolean byScore = Objects.equals(xpdRuleDto.getResultType(), XpdResultTypeEnum.SCORE.getCode());
            if (Objects.equals(xpdRuleDto.getResultType(), XpdResultTypeEnum.DIM_LEVEL_RESULT.getCode())) {
                RuleMainBase mainData = new RuleMainBase();
                mainData.setOrgId(orgId);
                mainData.setBizId(xpdId);
                Function<RuleColumnValParam, List<RuleColumnValueBean>> colValFunc = colValParam -> {
                    if (colValParam.getColumnType() == RvRuleTypeEnum.XPD_DIM_COUNT.columnType()) {
                        Map<String, Integer> gridLevelQty = new HashMap<>();
                        userResults.dimResultMap.forEach((key, dimResult) -> {
                            String levelId = dimResult.getGridLevelId();
                            levelId = Optional.ofNullable(levelId).orElse(StringPool.EMPTY);
                            if (StringUtils.isEmpty(levelId)) {
                                return;
                            }
                            Integer count = gridLevelQty.get(levelId);
                            if (count == null) {
                                count = 0;
                            }
                            count++;
                            gridLevelQty.put(levelId, count);
                        });
                        return gridLevelQty.entrySet().stream().map(entry -> {
                            RuleColumnValueBean colValBean = new RuleColumnValueBean();
                            colValBean.setColumnId(entry.getKey());
                            colValBean.setObjectId(userId);
                            colValBean.setValue(entry.getValue());
                            return colValBean;
                        }).collect(Collectors.toList());
                    } else {
                        return userResults.dimResultMap.values().stream().filter(dimResult -> StringUtils.isNotEmpty(dimResult.getGridLevelId())).map(dimResult -> {
                            String levelId = dimResult.getGridLevelId();
                            RuleColumnValueBean colValBean = new RuleColumnValueBean();
                            colValBean.setColumnId(dimResult.getSdDimId());
                            colValBean.setObjectId(userId);
                            colValBean.setEnumId(levelId);
                            return colValBean;
                        }).collect(Collectors.toList());
                    }
                };
                mainData.setColValFunc(colValFunc);
                matchXpdLevel = spRuleService.calcFirstMatch(mainData, userId, fullDto.getXpdRuleLevels(), XpdRuleLevelDto::getRuleConfig);
            } else if (StringUtils.isNotEmpty(fullDto.getXpdCalcFormula())) {
                BigDecimal scoreVal = null;
                if (fullDto.getXpdCaliCalcType() == XpdDimRule4Cali.TYPE_FORMULA && byIndicator && byScore) {
                    ExpressionCalc formulaCalc = FormulaTypeEnum.parseExpression(fullDto.getXpdCalcFormula());
                    try {
                        Double result = formulaCalc.calcResult(paramChip -> {
                            if (paramChip.getParamBean() == null) {
                                return BigDecimal.ZERO;
                            }
                            return formulaChipUserScore(fullDto.getFastFormulaIndicatorMap(), userResults, paramChip);
                        });
                        if (result != null && Double.isFinite(result)) {
                            scoreVal = CommonUtil.valueOf(result, MC_TWO);
                            scoreVal = convertToTargetSystem(scoreVal, xpdRuleDto.getCalcTotalScore(), fullDto.getScoreSystem());
                        }
                    } catch (Exception e) {
                        log.warn("calcCaliXpdIndicatorFormulaResult orgId {} xpdId {} failed {}", orgId, xpdId, e.getMessage());
                    }
                } else if (fullDto.getXpdCaliCalcType() == XpdDimRule4Cali.TYPE_BUILD_FORMULA) {
                    ExpressionCalc formulaCalc = new ExpressionCalc(fullDto.getXpdCalcFormula());
                    Double resultVal = null;
                    try {
                        resultVal = formulaCalc.calcResult(chip -> {
                            if (chip.getBizType() == 0) {
                                String sdIndicatorId = IArrayUtils.getFirst(chip.getParamList());
                                CaliUpdateUserResultReq resultDto = userResults.indResultMap.get(sdIndicatorId);
                                if (resultDto == null) {
                                    return null;
                                }
                                if (byScore) {
                                    return Optional.ofNullable(resultDto.getScoreValue()).orElse(BigDecimal.ZERO);
                                } else {
                                    return Optional.ofNullable(resultDto.getQualified()).orElse(0);
                                }
                            } else {
                                String sdDimId = IArrayUtils.getFirst(chip.getParamList());
                                CaliDimResultBean dimResult = userResults.dimResultMap.get(sdDimId);
                                if (dimResult == null || dimResult.isNoneResult()) {
                                    return null;
                                }
                                if (byScore) {
                                    return Optional.ofNullable(dimResult.getScoreValue())
                                        .orElse(BigDecimal.ZERO);
                                } else {
                                    return Optional.ofNullable(dimResult.getQualifiedPtg())
                                        .orElse(BigDecimal.ZERO);
                                }
                            }
                        });
                    } catch (Exception e) {
                        log.warn("calcCaliXpdFormulaResult orgId {} xpdId {} failed {}", orgId, xpdId, e.getMessage());
                    }
                    if (resultVal != null && Double.isFinite(resultVal)) {
                        scoreVal = CommonUtil.valueOf(resultVal, MC_TWO);
                    }
                }
                if (scoreVal != null) {
                    if (XpdLevelTypeEnum.needAfterResult(xpdRuleDto.getLevelType())) {
                        RatioLevelThresholdDto ruleThresholdDto = CommonUtils.tryParseObject(xpdRuleDto.getRuleThreshold(), RatioLevelThresholdDto.class);
                        if (XpdLevelTypeEnum.byRatio(xpdRuleDto.getLevelType())) {
                            String gridLevelId = calcRatioLevelId(ruleThresholdDto, scoreVal,
                                DimLevelPriorityEnum.LOW.getCode().equals(xpdRuleDto.getLevelPriority()));
                            matchXpdLevel = IArrayUtils.getFirstMatch(fullDto.getXpdRuleLevels(), rule -> rule.getId().equals(gridLevelId));
                        } else {
                            RuleMainBase mainData = new RuleMainBase();
                            mainData.setOrgId(orgId);
                            mainData.setBizId(xpdId);
                            mainData.setBizData(Optional.ofNullable(ruleThresholdDto).map(RatioLevelThresholdDto::getLevelScore).orElse(null));
                            BigDecimal finalScoreVal = scoreVal;
                            mainData.setColValFunc(colValParam -> {
                                RuleColumnValueBean valueBean = new RuleColumnValueBean();
                                valueBean.setColumnId(IArrayUtils.getFirst(colValParam.getColumnIds()));
                                valueBean.setObjectId(userId);
                                valueBean.setValue(finalScoreVal);
                                return Lists.newArrayList(valueBean);
                            });
                            matchXpdLevel = spRuleService.calcFirstMatch(mainData, userId, fullDto.getXpdRuleLevels(), XpdRuleLevelDto::getRuleConfig);
                        }
                    } else {
                        matchXpdLevel = minMatchLevelRule(fullDto.getXpdRuleLevels(), XpdRuleLevelDto::getLevelValue, scoreVal);
                    }
                    if (byScore) {
                        resp.setCaliScore(scoreVal);
                    } else {
                        resp.setCaliQualifiedPtg(scoreVal);
                    }
                }
            }
        }
        if (fillBeforeResult) {
            resp.setXpdLevelId(calimeetResultUserMapper.userLevelId(orgId, caliMeetId, userId));
            resp.setXpdLevelName(IArrayUtils.getFirstMatch(fullDto.getXpdRuleLevels(),
                ruleLevel -> ruleLevel.getId().equals(resp.getXpdLevelId()),
                XpdRuleLevelDto::getLevelName));
        }
        if (matchXpdLevel != null) {
            resp.setCaliLevelId(matchXpdLevel.getId());
            resp.setCaliLevelName(matchXpdLevel.getLevelName());
        }
        resultContainer.setCalcResult(resp);
    }

    private List<CaliDimCombResultDto> assembleDimCombResult(XpdCaliCalcFullDto fullDto, Map<String, CaliDimResultBean> dimResultMap) {
        List<CaliDimCombResultDto> combList = new ArrayList<>();
        CaliDimResultBean emptyDimResult = new CaliDimResultBean();
        Map<String, Integer> gridLevelIndexMap = StreamUtil.list2map(fullDto.getGridLevelList(), XpdGridLevelBriefDto::getId, XpdGridLevelBriefDto::getOrderIndex);
        fullDto.getCombList().forEach(comb -> {
            CaliDimResultBean xDimResult = dimResultMap.getOrDefault(comb.getXSdDimId(), emptyDimResult);
            CaliDimResultBean yDimResult = dimResultMap.getOrDefault(comb.getYSdDimId(), emptyDimResult);
            Integer xpdXLevelIndex = gridLevelIndexMap.get(xDimResult.getXpdGridLevelId());
            Integer xpdYLevelIndex = gridLevelIndexMap.get(yDimResult.getXpdGridLevelId());
            Integer caliXLevelIndex = gridLevelIndexMap.get(xDimResult.getGridLevelId());
            Integer caliYLevelIndex = gridLevelIndexMap.get(yDimResult.getGridLevelId());
            CaliDimCombResultDto combResult = new CaliDimCombResultDto();
            combResult.setCombId(comb.getId());
            combResult.setCombName(comb.getCombName());
            combResult.setXpdCellIndex(GridCellTemplateEnum.getCellIndexByLocation(fullDto.getGridType(), xpdXLevelIndex, xpdYLevelIndex));
            combResult.setXSdDimId(comb.getXSdDimId());
            combResult.setXSdDimName(fullDto.getSdDimNameMap().get(comb.getXSdDimId()));
            combResult.setYSdDimId(comb.getYSdDimId());
            combResult.setYSdDimName(fullDto.getSdDimNameMap().get(comb.getYSdDimId()));
            combResult.setCaliCellIndex(GridCellTemplateEnum.getCellIndexByLocation(fullDto.getGridType(), caliXLevelIndex, caliYLevelIndex));
            if (!CommonUtil.anyNull(xpdXLevelIndex, xpdYLevelIndex, caliXLevelIndex, caliYLevelIndex)) {
                combResult.setCaliGap(Math.abs(caliXLevelIndex - xpdXLevelIndex) + Math.abs(caliYLevelIndex - xpdYLevelIndex));
            }
            combResult.setAffectComb(xDimResult.isAffectDim() || yDimResult.isAffectDim());
            combList.add(combResult);
        });
        return combList;
    }

    private BigDecimal formulaChipUserScore(Map<String, Set<String>> fastRefIndMap,
        CaliUserResults userResults,
        ExpressionCalc.ExpressionChip paramChip) {
        if (paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_EVAL.getCode()) {
            FormulaAomActvParam param = (FormulaAomActvParam) paramChip.getParamBean();
            return userResults.aomResult(param.getActvId(), param.getSdIndicatorId(), param.getTypeId());
        } else if (paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_OTHER.getCode()) {
            FormulaAomActvParam param = (FormulaAomActvParam) paramChip.getParamBean();
            return userResults.aomResult(param.getActvId(), param.getSdIndicatorId(), null);
        } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_IMPORT.getCode()) {
            FormulaImportParam param = (FormulaImportParam) paramChip.getParamBean();
            return userResults.normalResult(DimRuleCalcRefEnum.IMPORT_DATA.getCode(),
                param.getImportId(), param.getSdIndicatorId());
        } else if (paramChip.getBizType() == FormulaTypeEnum.PRI_PROFILE.getCode()) {
            String sdIndicatorId = (String) paramChip.getParamBean();
            return userResults.normalResult(DimRuleCalcRefEnum.PRI_PROFILE.getCode(),
                null, sdIndicatorId);
        } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_FAST.getCode()) {
            FormulaFastParam param = (FormulaFastParam) paramChip.getParamBean();
            BigDecimal[] totalScoreRef = new BigDecimal[]{BigDecimal.ZERO};
            int[] countRef = new int[]{0};
            boolean hasEmpty = eachFormulaFastParamIndicator(param, fastRefIndMap, ref -> {
                BigDecimal oneIndScore = null;
                if (ref.ref.getRefType() == DimRuleCalcRefEnum.AOM_ACT.getCode()) {
                    oneIndScore = userResults.aomResult(ref.ref.getRefId(), ref.sdIndicatorId, param.getTypeId());
                } else {
                    oneIndScore = userResults.normalResult(ref.ref.getRefType(),
                        ref.ref.getRefId(), ref.sdIndicatorId);
                }
                if (oneIndScore != null) {
                    totalScoreRef[0] = totalScoreRef[0].add(oneIndScore);
                    countRef[0]++;
                }
                return oneIndScore == null;
            });
            if (hasEmpty) {
                return null;
            }
            int count = countRef[0];
            BigDecimal totalScore = totalScoreRef[0];
            if (SpEvalTypeEnum.isAvgType(param.getTypeId())) {
                return count == 0 ? BigDecimal.ZERO : totalScore.divide(BigDecimal.valueOf(count), MC_TWO.getPrecision(), MC_TWO.getRoundingMode());
            } else {
                return totalScore;
            }
        }
        return BigDecimal.ZERO;
    }

    private boolean eachFormulaFastParamIndicator(FormulaFastParam param,
        Map<String, Set<String>> fastRefIndMap, Predicate<FormulaRefIndicator> consumer) {
        int hasEmptyQty = 0;
        for (FormulaFastParam.FastRefBO refId : param.getRefIds()) {
            Set<String> indicatorIds = fastRefIndMap.get(doneMainKey(refId.getRefType(), refId.getRefId()));
            if (IArrayUtils.hasMatch(indicatorIds, indicatorId -> consumer.test(new FormulaRefIndicator(refId, indicatorId)))) {
                hasEmptyQty++;
            }
        }
        return hasEmptyQty > 0;
    }

    private void calcCaliDimResult(XpdCaliCalcDimParam param, XpdDimRule4Cali dimRule,
        String userId, BigDecimal getValue, Consumer<CaliDimResultBean> matchDimResult) {
        CaliDimResultBean dimResult = null;
        if (getValue != null) {
            String sdDimId = param.getSdDimId();
            boolean getScore = param.isByScore();
            if (DimLevelTypeEnum.needAfterResult(dimRule.getLevelType())) {
                if (!Objects.equals(dimRule.getCalcType(), DimCalcTypeEnum.PERF_RESULT.getCode())) {
                    if (DimLevelTypeEnum.byRatio(dimRule.getLevelType())) {
                        String gridLevelId = calcRatioLevelId(dimRule.getRuleThresholdDto(), getValue,
                            DimLevelPriorityEnum.LOW.getCode().equals(dimRule.getLevelPriority()));
                        if (StringUtils.isNotEmpty(gridLevelId)) {
                            dimResult = new CaliDimResultBean();
                            dimResult.setUserId(userId);
                            dimResult.setSdDimId(sdDimId);
                            dimResult.setGridLevelId(gridLevelId);
                            if (getScore) {
                                dimResult.setScoreValue(getValue);
                            } else {
                                dimResult.setQualifiedPtg(getValue);
                            }
                        }
                    } else {
                        //综合计算规则
                        RuleMainBase mainData = new RuleMainBase();
                        mainData.setOrgId(param.getOrgId());
                        mainData.setBizId(param.getXpdId());
                        mainData.setBizData(Optional.ofNullable(dimRule.getRuleThresholdDto())
                            .map(RatioLevelThresholdDto::getLevelScore).orElse(null));
                        mainData.setColValFunc(colValParam -> {
                            RuleColumnValueBean valueBean = new RuleColumnValueBean();
                            valueBean.setColumnId(IArrayUtils.getFirst(colValParam.getColumnIds()));
                            valueBean.setObjectId(userId);
                            valueBean.setValue(getValue);
                            return Lists.newArrayList(valueBean);
                        });
                        DimGridLevelRuleDTO levelRule = spRuleService.calcFirstMatch(mainData, userId, dimRule.getGridLevelRules(), DimGridLevelRuleDTO::getJudgeRule);
                        if (levelRule != null) {
                            dimResult = new CaliDimResultBean();
                            dimResult.setUserId(userId);
                            dimResult.setSdDimId(sdDimId);
                            dimResult.setGridLevelId(levelRule.getGridLevelId());
                            if (getScore) {
                                dimResult.setScoreValue(getValue);
                            } else {
                                dimResult.setQualifiedPtg(getValue);
                            }
                        }
                    }
                }
            } else {
                DimGridLevelRuleDTO levelRule = minMatchLevelRule(dimRule.getGridLevelRules(), DimGridLevelRuleDTO::getBaseValue, getValue);
                if (levelRule != null) {
                    dimResult = new CaliDimResultBean();
                    dimResult.setUserId(userId);
                    dimResult.setSdDimId(sdDimId);
                    dimResult.setGridLevelId(levelRule.getGridLevelId());
                    if (getScore) {
                        dimResult.setScoreValue(getValue);
                    } else {
                        dimResult.setQualifiedPtg(getValue);
                    }
                }
            }
        }
        if (dimResult == null) {
            dimResult = new CaliDimResultBean();
            dimResult.setNoneResult(true);
            dimResult.setUserId(userId);
            dimResult.setSdDimId(param.getSdDimId());
        }
        dimResult.setAffectDim(true);
        matchDimResult.accept(dimResult);
    }

    public void xpdFixDimRuleThreshold(String orgId, String xpdId) {
        CalcResultRefContext refContext = initCalcRefContext(orgId, xpdId, false);
        if (refContext == null) {
            return;
        }
        boolean getDimScore = refContext.resultType == DimResultTypeEnum.SCORE_VALUE.getCode();
        for (DimRuleMetaDTO dimRuleMeta : refContext.allXpdDimList) {
            Integer levelType = Optional.ofNullable(dimRuleMeta.dimRuleBase)
                .map(XpdDimRuleDto::getLevelType).orElse(null);
            //不是按绩效结果，按比例计算时
            if (DimLevelTypeEnum.byRatio(levelType) && !Objects.equals(dimRuleMeta.dimRuleBase.getCalcType(), DimCalcTypeEnum.PERF_RESULT.getCode())) {
                log.info("xpdFixDimRuleThreshold {} dimGridLevelCalcByPtg sdDimId {} coverByImport {}", refContext.logPrefix(),
                    dimRuleMeta.sdDimId, dimRuleMeta.coverByImport);
                if (dimRuleMeta.coverByImport) {
                    //导入的结果不需要计算分层
                    continue;
                }
                RatioLevelThresholdDto thresholdDto = new RatioLevelThresholdDto();
                thresholdDto.setInvalidScore(YesOrNo.YES.getValue());
                try {
                    List<DimGridLevelRuleDTO> gridLevelRules = dimRuleMeta.dimRuleBase.getGridLevelRules();
                    if (CollectionUtils.isEmpty(gridLevelRules)) {
                        log.info("xpdFixDimRuleThreshold dimGridLevelCalcByPtg emptyRules batchNo {} orgId {} xpdId {} sdDimId {}",
                            refContext.calcBatch.getBatchNo(), refContext.orgId, refContext.xpdId, dimRuleMeta.sdDimId);
                        continue;
                    }
                    List<DecimalPtgBean> ptgBeans = xpdResultUserDimMapper.listSortValue(refContext.orgId,
                        refContext.xpdId, dimRuleMeta.sdDimId,
                        //全局得分配置或者绩效得分
                        getDimScore || Objects.equals(dimRuleMeta.dimRuleBase.getCalcType(), DimCalcTypeEnum.PERF_SCORE.getCode()));
                    if (ptgBeans.isEmpty()) {
                        continue;
                    }
                    //gridLevelRules 分层顺序从高到低
                    if (DimLevelPriorityEnum.LOW.getCode().equals(dimRuleMeta.dimRuleBase.getLevelPriority())) {
                        //从低到高排序
                        IArrayUtils.sortList(ptgBeans, DecimalPtgBean::getSortValue);
                        //分层顺序反转为从低到高
                        Collections.reverse(gridLevelRules);
                    } else {
                        //从高到低排序
                        IArrayUtils.sortListDesc(ptgBeans, DecimalPtgBean::getSortValue);
                    }
                    thresholdDto = calcLevelMatchByPtg(ptgBeans, gridLevelRules, DimGridLevelRuleDTO::getBaseValue, DimGridLevelRuleDTO::getGridLevelId);
                } finally {
                    xpdDimRuleMapper.updateRuleThreshold(dimRuleMeta.dimRuleBase.getId(), JSON.toJSONString(thresholdDto), thresholdDto.getInvalidScore());
                }
            }
        }
        XpdRulePO xpdRulePO = xpdRuleMapper.getByXpdId(orgId, xpdId);
        if (xpdRulePO == null) {
            log.info("xpdFixDimRuleThreshold {} XpdRulePO is null", refContext.logPrefix());
            return;
        }
        Integer resultType = xpdRulePO.getResultType();
        if (XpdLevelTypeEnum.byRatio(xpdRulePO.getLevelType())
            && !Objects.equals(resultType, XpdResultTypeEnum.DIM_LEVEL_RESULT.getCode())) {
            log.info("{} calcXpdLevelByPtg condition match", refContext.logPrefix());
            //按比例&不是分层结果的
            boolean getXpdScore = Objects.equals(XpdResultTypeEnum.SCORE.getCode(), resultType);
            List<DecimalPtgBean> ptgBeans = xpdResultUserMapper.listSortValue(orgId, xpdId, getXpdScore);
            RatioLevelThresholdDto thresholdDto = new RatioLevelThresholdDto();
            thresholdDto.setInvalidScore(YesOrNo.YES.getValue());
            try {
                if (ptgBeans.isEmpty()) {
                    return;
                }
                //xpdRuleLevels 分层顺序从高到低
                List<XpdRuleLevelDto> xpdRuleLevels = BeanCopierUtil.convertList(xpdLevelMapper.queryByRuleId(orgId, xpdId, xpdRulePO.getId()),
                    xpdLevelPO -> {
                        XpdRuleLevelDto levelDto = new XpdRuleLevelDto();
                        BeanCopierUtil.copy(xpdLevelPO, levelDto);
                        return levelDto;
                    });
                if (DimLevelPriorityEnum.LOW.getCode().equals(xpdRulePO.getLevelPriority())) {
                    //从低到高排序
                    IArrayUtils.sortList(ptgBeans, DecimalPtgBean::getSortValue);
                    //分层顺序反转为从低到高
                    Collections.reverse(xpdRuleLevels);
                } else {
                    //从高到低排序
                    IArrayUtils.sortListDesc(ptgBeans, DecimalPtgBean::getSortValue);
                }
                thresholdDto = calcLevelMatchByPtg(ptgBeans, xpdRuleLevels, XpdRuleLevelDto::getLevelValue, XpdRuleLevelDto::getId);
            } finally {
                xpdRuleMapper.updateRuleThreshold(xpdRulePO.getId(), JSON.toJSONString(thresholdDto), thresholdDto.getInvalidScore());
            }
        }
    }

    public XpdCaliCalcFullDto buildXpdCalcFormula(String orgId, String xpdId) {
        String cacheKey = String.format(RedisKeys.CK_CALI_XPD_CALC_FORMULA, xpdId);
        return cacheRepository.cache(cacheKey, 2, TimeUnit.HOURS, () -> {
            CalcResultRefContext refContext = initCalcRefContext(orgId, xpdId, false);
            if (refContext == null) {
                return Optional.empty();
            }
            int resultType = refContext.resultType;
            List<XpdDimRule4Cali> dimRules = new ArrayList<>();
            Map<String, String> sdDimNameMap = new HashMap<>();
            Map<String, XpdRuleRefIndicatorsDto> allRefIndicatorsMap = new HashMap<>();
            Consumer<List<XpdDimRuleIndicatorRefDto>> addRefIndFunc = indRefs -> {
                if (indRefs != null) {
                    for (XpdDimRuleIndicatorRefDto indRef : indRefs) {
                        String key = doneMainKey(indRef.getRefType(), indRef.getRefId());
                        allRefIndicatorsMap.computeIfAbsent(key, paramKey -> {
                            XpdRuleRefIndicatorsDto refInds = new XpdRuleRefIndicatorsDto();
                            refInds.setRefType(indRef.getRefType());
                            refInds.setRefId(indRef.getRefId());
                            refInds.setIndicatorIds(new HashSet<>());
                            return refInds;
                        }).getIndicatorIds().add(indRef.getIndicatorId());
                    }
                }
            };
            for (DimRuleMetaDTO dimRuleMeta : refContext.allXpdDimList) {
                Optional.ofNullable(refContext.dimIndicatorsMap.get(dimRuleMeta.sdDimId))
                    .map(Pair::getKey).ifPresent(dimBase -> sdDimNameMap.put(dimRuleMeta.sdDimId, dimBase.getDmName()));
                XpdDimRuleDto dimRuleDto = dimRuleMeta.dimRuleBase;
                XpdDimRule4Cali dimRule = new XpdDimRule4Cali();
                dimRule.setSdDimId(dimRuleMeta.sdDimId);
                dimRule.setTotalScore(dimRuleMeta.savedScoreTotal);
                if (dimRuleDto != null) {
                    dimRule.setCalcType(dimRuleDto.getCalcType());
                    dimRule.setResultType(dimRuleDto.getResultType());
                    dimRule.setLevelType(dimRuleDto.getLevelType());
                    dimRule.setLevelPriority(dimRuleDto.getLevelPriority());
                    dimRule.setGridLevelRules(dimRuleDto.getGridLevelRules());
                    dimRule.setRuleThresholdDto(dimRuleDto.getRuleThresholdDto());
                    dimRule.setCalcTotalScore(dimRuleDto.getCalcTotalScore());
                }
                if (dimRuleMeta.coverByImport || dimRuleMeta.dimRuleBase == null) {
                    //导入的或者没有规则
                    dimRule.setCaliCalcType(XpdDimRule4Cali.TYPE_NONE);
                } else if (DimCalcTypeEnum.isPerf(dimRuleMeta.dimRuleBase.getCalcType())) {
                    String aomActId = dimRuleMeta.dimRuleBase.getAomActId();
                    AomActvExtBO actvExtBO = refContext.aomActvMap.get(aomActId);
                    String sdIndicatorId = Optional.ofNullable(actvExtBO).map(AomActvExtBO::getIndicators)
                        .map(IArrayUtils::getFirst).map(AomActvIndicatorDto::getSdIndicatorId).orElse(null);
                    dimRule.setPerfSdIndicatorId(sdIndicatorId);
                    dimRule.setCaliCalcType(XpdDimRule4Cali.TYPE_PERF);
                    dimRule.setPerfAomActId(aomActId);
                } else if (Objects.equals(dimRuleMeta.dimRuleBase.getCalcType(), DimCalcTypeEnum.INDICATOR.getCode())) {
                    if (Objects.equals(DimCalcRuleEnum.FORMULA.getCode(), dimRuleMeta.dimRuleBase.getCalcRule())) {
                        continue;
                        //6.3-REVERT001：当前逻辑有高级公式不参与校准
                        //dimRule.setCaliCalcType(XpdDimRule4Cali.TYPE_FORMULA);
                        //dimRule.setCalcFormula(dimRuleMeta.dimRuleBase.getFormula());
                        //addRefIndFunc.accept(dimRuleMeta.ruleIndicatorRefs);
                        //if (dimRuleMeta.ruleIndicatorRefs != null) {
                        //    dimRule.setFormulaSdIndicatorSet(dimRuleMeta.ruleIndicatorRefs.stream()
                        //        .map(XpdDimRuleIndicatorRefDto::getIndicatorId).collect(Collectors.toSet()));
                        //}
                    } else if (dimRuleMeta.ruleCalcList != null) {
                        Set<String> formulaSdIndicatorSet = new HashSet<>();
                        StringBuilder calcExp = new StringBuilder();
                        appendDimRuleCalcList(resultType, formulaSdIndicatorSet, calcExp, dimRuleMeta.ruleCalcList);
                        dimRule.setCaliCalcType(XpdDimRule4Cali.TYPE_BUILD_FORMULA);
                        dimRule.setCalcFormula(calcExp.toString());
                        dimRule.setFormulaSdIndicatorSet(formulaSdIndicatorSet);
                    }
                } else if (CollectionUtils.isNotEmpty(dimRuleMeta.subDimRules)) {
                    Set<String> formulaSdIndicatorSet = new HashSet<>();
                    StringBuilder calcExp = new StringBuilder();
                    buildSubDimRuleFormula(resultType, formulaSdIndicatorSet, calcExp, dimRuleMeta.subDimRules);
                    dimRule.setCaliCalcType(XpdDimRule4Cali.TYPE_BUILD_FORMULA);
                    dimRule.setCalcFormula(calcExp.toString());
                    dimRule.setFormulaSdIndicatorSet(formulaSdIndicatorSet);
                } else {
                    dimRule.setCaliCalcType(XpdDimRule4Cali.TYPE_NONE);
                }
                dimRules.add(dimRule);
            }
            XpdCaliCalcFullDto ret = new XpdCaliCalcFullDto();
            ret.setOrgId(orgId);
            ret.setResultType(resultType);
            ret.setRuleList(dimRules);
            ret.setCombList(xpdDimCombMapper.listBriefByXpdId(orgId, xpdId));
            ret.setSdDimNameMap(sdDimNameMap);
            Set<String> usedSdDimIds = new HashSet<>();
            ret.getCombList().forEach(comb -> {
                usedSdDimIds.add(comb.getXSdDimId());
                usedSdDimIds.add(comb.getYSdDimId());
            });
            ret.setCombUsedDimSet(usedSdDimIds);
            Integer gridType = xpdGridMapper.getGridTypeById(refContext.gridId);
            ret.setGridLevelList(BeanCopierUtil.convertList(xpdGridLevelMapper.listByGridId(orgId, refContext.gridId), gridLevel -> {
                XpdGridLevelBriefDto levelDto = new XpdGridLevelBriefDto();
                BeanCopierUtil.copy(gridLevel, levelDto);
                return levelDto;
            }));
            ret.setGridType(gridType);
            ret.setScoreSystem(refContext.scoreSystem);
            ret.setRuleRefIndicators(refContext.refIndicatorMap.values().stream()
                .map(refInd -> {
                    XpdRuleRefIndicatorDto refDto = new XpdRuleRefIndicatorDto();
                    refDto.setRefId(refInd.refId);
                    refDto.setRefType(refInd.refType);
                    refDto.setIndicatorIds(refInd.indicatorMap.keySet());
                    return refDto;
                }).collect(Collectors.toList()));
            ret.setSdIndicatorMap(new HashMap<>());
            Map<String, BigDecimal> indTotalScoreMap = StreamUtil.list2map(xpdResultIndicatorMapper.findAllByXpdId(orgId, xpdId),
                XpdResultIndicatorPO::getSdIndicatorId, XpdResultIndicatorPO::getScoreTotal);
            refContext.modelIndicatorsMap.forEach((sdIndId, sdInd)
                -> ret.getSdIndicatorMap().put(sdIndId,
                new XpdSdIndicatorScoreDto(
                    StringUtils.isEmpty(sdInd.getIndicatorName()) ? sdInd.getItemValue() : sdInd.getIndicatorName(),
                    sdInd.getNum(),
                    //当前逻辑没有算出得分都是高级公式里选的指标不在规则里也不在活动里的，固定总分10(10分制
                    indTotalScoreMap.getOrDefault(sdIndId, BigDecimal.TEN)
                )));
            ret.setAomPrjId(refContext.aomPrjId);
            //项目结果
            XpdRulePO xpdRulePO = xpdRuleMapper.getByXpdId(orgId, xpdId);
            if (xpdRulePO != null) {
                log.info("{} XpdRulePO is null", refContext.logPrefix());
                //清除维度计算用的指标关联map
                refContext.refIndicatorMap.clear();
                buildXpdRefContext(refContext, xpdRulePO);
                prepareRefContext(refContext);
                XpdRuleMainDto xpdRuleDto = refContext.xpdRuleDto;
                if (Objects.equals(XpdCalcTypeEnum.BY_INDICATOR.getCode(), xpdRuleDto.getCalcType())) {
                    boolean byScore = Objects.equals(xpdRuleDto.getResultType(), XpdResultTypeEnum.SCORE.getCode());
                    if (byScore && Objects.equals(xpdRuleDto.getCalcRule(), XpdCalcRuleEnum.FORMULA.getCode())) {
                        ret.setXpdCaliCalcType(XpdDimRule4Cali.TYPE_NONE);
                        //6.3-REVERT001：当前逻辑有高级公式不参与校准
                        //ret.setXpdCaliCalcType(XpdDimRule4Cali.TYPE_FORMULA);
                        //ret.setXpdCalcFormula(xpdRuleDto.getFormula());
                        //addRefIndFunc.accept(refContext.xpdRuleIndicatorRefs);
                    } else {
                        StringBuilder calcExp;
                        if (byScore) {
                            calcExp = new StringBuilder("0");
                            if (refContext.xpdCalcIndRules != null) {
                                for (XpdRuleCalcIndicatorDto xpdCalcIndRule : refContext.xpdCalcIndRules) {
                                    calcExp.append("+")
                                        .append(ExpressionCalc.buildParam(0, xpdCalcIndRule.getSdIndicatorId()))
                                        .append("*").append(multiplyPtg(BigDecimal.ONE, xpdCalcIndRule.getWeight()));
                                }
                            }
                        } else {
                            if (CollectionUtils.isEmpty(refContext.xpdCalcIndRules)) {
                                calcExp = new StringBuilder("0");
                            } else {
                                calcExp = new StringBuilder("(0");
                                for (XpdRuleCalcIndicatorDto ruleCalcDto : refContext.xpdCalcIndRules) {
                                    calcExp.append("+").append(ExpressionCalc.buildParam(0, ruleCalcDto.getSdIndicatorId()));
                                }
                                calcExp.append(")*100/").append((double) refContext.xpdCalcIndRules.size());
                            }
                        }
                        ret.setXpdCaliCalcType(XpdDimRule4Cali.TYPE_BUILD_FORMULA);
                        ret.setXpdCalcFormula(calcExp.toString());
                    }
                } else if (Objects.equals(XpdResultTypeEnum.SCORE.getCode(), xpdRuleDto.getResultType())) {
                    StringBuilder calcExp = new StringBuilder("0");
                    if (refContext.xpdCalcDimRules != null) {
                        for (XpdRuleCalcDimPO xpdCalcDimRule : refContext.xpdCalcDimRules) {
                            calcExp.append("+")
                                .append(ExpressionCalc.buildParam(1, xpdCalcDimRule.getSdDimId()))
                                .append("*").append(multiplyPtg(BigDecimal.ONE, xpdCalcDimRule.getWeight()));
                        }
                    }
                    ret.setXpdCaliCalcType(XpdDimRule4Cali.TYPE_BUILD_FORMULA);
                    ret.setXpdCalcFormula(calcExp.toString());
                } else if (Objects.equals(XpdResultTypeEnum.RATIO.getCode(), xpdRuleDto.getResultType())) {
                    StringBuilder calcExp = new StringBuilder();
                    List<DimRuleMetaDTO> xpdDimList = Optional.ofNullable(refContext.allXpdDimList).orElse(Lists.newArrayList())
                        .stream().filter(dim -> !dim.coverByImport).collect(Collectors.toList());
                    if (!xpdDimList.isEmpty()) {
                        calcExp.append("(0");
                        for (DimRuleMetaDTO dimRule : xpdDimList) {
                            calcExp.append("+").append(ExpressionCalc.buildParam(1, dimRule.sdDimId));
                        }
                        calcExp.append(")/" + xpdDimList.size());
                    } else {
                        calcExp.append("0");
                    }
                    ret.setXpdCaliCalcType(XpdDimRule4Cali.TYPE_BUILD_FORMULA);
                    ret.setXpdCalcFormula(calcExp.toString());
                }
                ret.setXpdRuleLevels(refContext.xpdRuleLevels);
                XpdRule4CaliDto xpdRule4Cali = new XpdRule4CaliDto();
                BeanCopierUtil.copy(xpdRuleDto, xpdRule4Cali);
                ret.setXpdRuleDto(xpdRule4Cali);
            }
            ret.setFastFormulaIndicatorMap(refContext.fastFormulaIndicatorMap);
            ret.setAllFormulaRefIndicators(allRefIndicatorsMap.values().stream().toList());
            return Optional.of(ret);
        }, XpdCaliCalcFullDto.class).orElse(null);
    }

    private void buildSubDimRuleFormula(int resultType, Set<String> formulaSdIndicatorSet, StringBuilder calcExp, List<DimRuleMetaDTO> subDimRules) {
        calcExp.append("0");
        for (DimRuleMetaDTO subDimRule : subDimRules) {
            if (subDimRule.dimRuleBase == null) {
                continue;
            }
            calcExp.append("+(");
            if (CollectionUtils.isNotEmpty(subDimRule.subDimRules)) {
                buildSubDimRuleFormula(resultType, formulaSdIndicatorSet, calcExp, subDimRule.subDimRules);
            } else {
                appendDimRuleCalcList(resultType, formulaSdIndicatorSet, calcExp, subDimRule.ruleCalcList);
            }
            calcExp.append(")*")
                .append(multiplyPtg(BigDecimal.ONE, subDimRule.dimRuleBase.getWeight()));
        }
    }

    private void appendDimRuleCalcList(int resultType, Set<String> formulaSdIndicatorSet, StringBuilder calcExp, List<XpdDimRuleCalcDto> ruleCalcList) {
        if (DimResultTypeEnum.byScore(resultType)) {
            calcExp.append("0");
            if (ruleCalcList != null) {
                for (XpdDimRuleCalcDto ruleCalcDto : ruleCalcList) {
                    calcExp.append("+").append(ExpressionCalc.PARAM_START)
                        .append(ruleCalcDto.getSdIndicatorId())
                        .append(ExpressionCalc.PARAM_END).append("*")
                        .append(multiplyPtg(BigDecimal.ONE, ruleCalcDto.getWeight()));
                    formulaSdIndicatorSet.add(ruleCalcDto.getSdIndicatorId());
                }
            }
        } else {
            if (CollectionUtils.isEmpty(ruleCalcList)) {
                calcExp.append("0");
            } else {
                calcExp.append("(0");
                for (XpdDimRuleCalcDto ruleCalcDto : ruleCalcList) {
                    calcExp.append("+").append(ExpressionCalc.PARAM_START)
                        .append(ruleCalcDto.getSdIndicatorId())
                        .append(ExpressionCalc.PARAM_END);
                    formulaSdIndicatorSet.add(ruleCalcDto.getSdIndicatorId());
                }
                calcExp.append(")*100/").append((double) ruleCalcList.size());
            }
        }
    }

    private List<DimRuleMetaDTO> buildSubDimRule(String pRuleId,
            List<XpdDimRuleDto> dimRulePool,
            Map<String, List<XpdDimRuleCalcDto>> ruleCalcMap) {
        List<XpdDimRuleDto> subDimRules = dimRulePool.stream().filter(dimRule -> Objects.equals(pRuleId, dimRule.getParentId()))
                .collect(Collectors.toList());
        return BeanCopierUtil.convertList(subDimRules, dimRulePO -> {
            dimRulePool.remove(dimRulePO);
            DimRuleMetaDTO dimRuleDTO = new DimRuleMetaDTO();
            dimRuleDTO.dimRuleBase = dimRulePO;
            dimRuleDTO.subDimRules = buildSubDimRule(dimRulePO.getId(), dimRulePool, ruleCalcMap);
            dimRuleDTO.ruleCalcList = ruleCalcMap.get(dimRulePO.getId());
            return dimRuleDTO;
        });
    }

    private void buildRefContext(CalcResultRefContext refContext, List<DimRuleMetaDTO> dimRuleList) {
        //导入设置
        for (DimRuleMetaDTO dimRuleMetaDTO : dimRuleList) {
            IArrayUtils.addMapList(refContext.allDimListMap, dimRuleMetaDTO.sdDimId, dimRuleMetaDTO);
            if (dimRuleMetaDTO.dimRuleBase == null) {
                continue;
            }
            if (DimCalcTypeEnum.isPerf(dimRuleMetaDTO.dimRuleBase.getCalcType())) {
                String aomActId = dimRuleMetaDTO.dimRuleBase.getAomActId();
                AomActvExtBO actvExtBO = refContext.aomActvMap.get(aomActId);
                AomActvIndicatorDto indicatorDto = Optional.ofNullable(actvExtBO).map(AomActvExtBO::getIndicators)
                    .map(IArrayUtils::getFirst).orElse(null);
                if (indicatorDto != null) {
                    dimRuleMetaDTO.dimRuleBase.setSdIndicatorId(indicatorDto.getSdIndicatorId());
                    refContext.addRefList(dimRuleMetaDTO.dimRuleBase.getSdIndicatorId(),
                        true, DimRuleCalcRefEnum.AOM_ACT.getCode(), aomActId);
                }
            } else if (Objects.equals(dimRuleMetaDTO.dimRuleBase.getCalcType(), DimCalcTypeEnum.INDICATOR.getCode())) {
                if (Objects.equals(DimCalcRuleEnum.FORMULA.getCode(), dimRuleMetaDTO.dimRuleBase.getCalcRule())) {
                    dimRuleMetaDTO.ruleIndicatorRefs = formulaFixAddRefToContext(refContext, dimRuleMetaDTO.dimRuleBase.getFormulaCalc());
                } else if (dimRuleMetaDTO.ruleCalcList != null) {
                    for (XpdDimRuleCalcDto ruleCalcDto : dimRuleMetaDTO.ruleCalcList) {
                        refContext.addRefList(ruleCalcDto.getSdIndicatorId(), true, ruleCalcDto.getRefList());
                        refContext.indicatorRuleCfgMap.put(ruleCalcDto.getSdIndicatorId(), ruleCalcDto);
                    }
                }
            } else if (CollectionUtils.isNotEmpty(dimRuleMetaDTO.subDimRules)) {
                buildRefContext(refContext, dimRuleMetaDTO.subDimRules);
            }
        }
        Set<String> notUsedIndicatorIds = new HashSet<>();
        refContext.modelIndicatorsMap.forEach((indicatorId, indicator) -> {
            if (!refContext.usedCalcRuleIndicaotrSet.contains(indicatorId)) {
                notUsedIndicatorIds.add(indicatorId);
            }
        });
        refContext.notUsedIndicatorIds = notUsedIndicatorIds;
        refContext.usedIndicaotrInfoMap = spsdAclService.getIndicatorInfoMap(
            refContext.orgId, refContext.modelId,
            refContext.usedIndicaotrInfoMap.keySet().stream().toList());
    }

    private void buildXpdRefContext(CalcResultRefContext refContext, XpdRulePO xpdRulePO) {
        log.info("{} buildXpdRefContext", refContext.logPrefix());
        //po转bo
        XpdRuleMainDto ruleMainDto = new XpdRuleMainDto();
        BeanCopierUtil.copy(xpdRulePO, ruleMainDto);
        ruleMainDto.setFormulaCalc(FormulaTypeEnum.parseExpression(xpdRulePO.getFormula()));
        //关联数据
        List<XpdRuleCalcDimPO> xpdCalcDimRules = xpdRuleCalcDimMapper.getByXpdId(refContext.orgId, refContext.xpdId);
        List<XpdRuleCalcIndicatorDto> xpdCalcIndRules = BeanCopierUtil.convertList(xpdRuleCalcIndicatorMapper.getByXpdId(refContext.orgId, refContext.xpdId), ruleCalc -> {
            XpdRuleCalcIndicatorDto calcDto = new XpdRuleCalcIndicatorDto();
            BeanCopierUtil.copy(ruleCalc, calcDto);
            calcDto.setRefList(CommonUtil.tryParseArray(ruleCalc.getRefIds(), XpdDimRuleCalcRefDto.class));
            return calcDto;
        });
        if (Objects.equals(XpdCalcTypeEnum.BY_INDICATOR.getCode(), xpdRulePO.getCalcType())) {
            //按指标结果计算
            if (Objects.equals(XpdCalcRuleEnum.FORMULA.getCode(), xpdRulePO.getCalcRule())) {
                refContext.xpdRuleIndicatorRefs = formulaFixAddRefToContext(refContext, ruleMainDto.getFormulaCalc());
            } else {
                for (XpdRuleCalcIndicatorDto ruleCalcDto : xpdCalcIndRules) {
                    refContext.addRefList(ruleCalcDto.getSdIndicatorId(),false, ruleCalcDto.getRefList());
                }
            }
        }
        //数据设置到refContext
        refContext.xpdCalcDimRules = xpdCalcDimRules;
        refContext.xpdCalcIndRules = xpdCalcIndRules;
        refContext.xpdRuleDto = ruleMainDto;
        refContext.xpdRuleLevels = BeanCopierUtil.convertList(xpdLevelMapper.queryByRuleId(refContext.orgId, refContext.xpdId, xpdRulePO.getId()),
            xpdLevelPO -> {
                XpdRuleLevelDto levelDto = new XpdRuleLevelDto();
                BeanCopierUtil.copy(xpdLevelPO, levelDto);
                levelDto.setRuleConfig(JSON.parseObject(xpdLevelPO.getFormula(), SpRuleBean.class));
                return levelDto;
            });
        refContext.usedIndicaotrInfoMap = spsdAclService.getIndicatorInfoMap(
            refContext.orgId, refContext.modelId,
            refContext.usedIndicaotrInfoMap.keySet().stream().toList());
    }

    private String calcRatioLevelId(RatioLevelThresholdDto ruleThresholdDto, BigDecimal value, boolean lowFirst) {
        if (ruleThresholdDto != null && !CollectionUtils.sizeIsEmpty(ruleThresholdDto.getLevelScore())) {
            BigDecimal previewMatchSocre = null;
            String matchLevelId = null;
            for (Map.Entry<String, BigDecimal> levelAndScore : ruleThresholdDto.getLevelScore().entrySet()) {
                BigDecimal levelScore = levelAndScore.getValue();
                if (lowFirst) {
                    //目标值小于等于等级数值
                    if (value.compareTo(levelScore) <= 0
                        && (previewMatchSocre == null || levelScore.compareTo(previewMatchSocre) < 0)) {
                        matchLevelId = levelAndScore.getKey();
                        previewMatchSocre = levelScore;
                    }
                } else {
                    //目标值大于等于等级数值
                    if (value.compareTo(levelScore) >= 0
                        && (previewMatchSocre == null || levelScore.compareTo(previewMatchSocre) > 0)) {
                        matchLevelId = levelAndScore.getKey();
                        previewMatchSocre = levelScore;
                    }
                }
            }
            return StringUtils.isNotEmpty(matchLevelId) ? matchLevelId : ruleThresholdDto.getDefaultLevelId();
        }
        return null;
    }

    private BigDecimal formulaResult(CalcResultRefContext refContext,
        ExpressionCalc formulaCalc, Pair<Integer, String> targetPair) {
        //calc checked
        if (formulaCalc == null) {
            return BigDecimal.ZERO;
        }
        try {
            Double result = formulaCalc.calcResult(paramChip -> {
                if (paramChip.getParamBean() == null) {
                    return BigDecimal.ZERO;
                }
                if (targetPair.getKey() == CALC_USER) {
                    return formulaChipUserScore(refContext, paramChip, targetPair.getValue());
                } else {
                    return formulaChipTotalScore(refContext, paramChip);
                }
            });
            if (result == null || !Double.isFinite(result)) {
                return null;
            }
            return BigDecimal.valueOf(result);
        } catch (ExpressNullValueException e) {
            return null;
        }
    }

    private BigDecimal formulaChipUserScore(CalcResultRefContext refContext,
        ExpressionCalc.ExpressionChip paramChip, String userId) {
        if (paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_EVAL.getCode()) {
            FormulaAomActvParam param = (FormulaAomActvParam) paramChip.getParamBean();
            return aomUserScore(refContext, param.getActvId(), param.getSdIndicatorId(), userId, param.getTypeId());
        } else if (paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_OTHER.getCode()) {
            FormulaAomActvParam param = (FormulaAomActvParam) paramChip.getParamBean();
            return aomUserScore(refContext, param.getActvId(), param.getSdIndicatorId(), userId, null);
        } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_IMPORT.getCode()) {
            FormulaImportParam param = (FormulaImportParam) paramChip.getParamBean();
            DimCalcUserResultDto resultDto = refContext.queryUserResult(
                DimRuleCalcRefEnum.IMPORT_DATA.getCode(),
                param.getImportId(),
                param.getSdIndicatorId(), userId);
            return nullAsZero(resultDto, DimCalcUserResultDto::getScoreValue);
        } else if (paramChip.getBizType() == FormulaTypeEnum.PRI_PROFILE.getCode()) {
            String sdIndicatorId = (String) paramChip.getParamBean();
            DimCalcUserResultDto resultDto = refContext.queryUserResult(
                DimRuleCalcRefEnum.PRI_PROFILE.getCode(),
                null,
                sdIndicatorId, userId);
            return nullAsZero(resultDto, DimCalcUserResultDto::getScoreValue);
        } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_FAST.getCode()) {
            FormulaFastParam param = (FormulaFastParam) paramChip.getParamBean();
            BigDecimal[] totalScoreRef = new BigDecimal[]{BigDecimal.ZERO};
            int[] countRef = new int[]{0};
            boolean hasEmpty = eachFormulaFastParamIndicator(refContext, param, ref -> {
                BigDecimal oneIndScore = null;
                if (ref.ref.getRefType() == DimRuleCalcRefEnum.AOM_ACT.getCode()) {
                    oneIndScore = aomUserScore(refContext, ref.ref.getRefId(), ref.sdIndicatorId, userId, param.getTypeId());
                } else {
                    DimCalcUserResultDto resultDto = refContext.queryUserResult(ref.ref.getRefType(),
                        ref.ref.getRefId(), ref.sdIndicatorId, userId);
                    oneIndScore = nullAsZero(resultDto, DimCalcUserResultDto::getScoreValue);
                }
                if (oneIndScore != null) {
                    totalScoreRef[0] = totalScoreRef[0].add(oneIndScore);
                    countRef[0]++;
                }
                return oneIndScore == null;
            });
            if (hasEmpty) {
                return null;
            }
            int count = countRef[0];
            BigDecimal totalScore = totalScoreRef[0];
            if (SpEvalTypeEnum.isAvgType(param.getTypeId())) {
                return count == 0 ? BigDecimal.ZERO : totalScore.divide(BigDecimal.valueOf(count), MC_TWO.getPrecision(), MC_TWO.getRoundingMode());
            } else {
                return totalScore;
            }
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal aomUserScore(CalcResultRefContext refContext, String actvId,
        String sdIndicatorId, String userId, String typeId) {
        DimCalcUserResultDto resultDto = refContext.queryAomUserResult(refContext,
            actvId, sdIndicatorId, userId);
        if (resultDto == null) {
            return null;
        }
        BigDecimal scoreValue = null;
        if ( StringUtils.isEmpty(typeId) || !NumberUtil.isNumber(typeId)) {
            //非测评结果
            scoreValue = resultDto.getScoreValue();
        } else {
            //测评结果
            scoreValue = SpEvalTypeEnum.evalTypeValue(typeId, resultDto);
        }
        return nullAsZero(scoreValue);
    }

    private BigDecimal formulaChipTotalScore(CalcResultRefContext refContext,
        ExpressionCalc.ExpressionChip paramChip) {
        //calc-checked
        if (paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_EVAL.getCode()
            || paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_OTHER.getCode()) {
            FormulaAomActvParam param = (FormulaAomActvParam) paramChip.getParamBean();
            RefIndicatorInfo indicatorInfo = refContext.queryIndicatorInfo(DimRuleCalcRefEnum.AOM_ACT.getCode(),
                param.getActvId(), param.getSdIndicatorId());
            //没有则是配置有问题,返回0
            return nullAsZero(indicatorInfo, RefIndicatorInfo::getTotalScore);
        } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_IMPORT.getCode()) {
            FormulaImportParam param = (FormulaImportParam) paramChip.getParamBean();
            RefIndicatorInfo indicatorInfo = refContext.queryIndicatorInfo(DimRuleCalcRefEnum.IMPORT_DATA.getCode(),
                param.getImportId(), param.getSdIndicatorId());
            //没有则是配置有问题,返回0
            return nullAsZero(indicatorInfo, RefIndicatorInfo::getTotalScore);
        } else if (paramChip.getBizType() == FormulaTypeEnum.PRI_PROFILE.getCode()) {
            //固定总分10(10分制
            return BigDecimal.TEN;
        } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_FAST.getCode()) {
            FormulaFastParam param = (FormulaFastParam) paramChip.getParamBean();
            Pair<Integer, BigDecimal> countAndScore = calcFormulaFastParamTotalScore(refContext, param);
            BigDecimal totalScore = countAndScore.getValue();
            int count = countAndScore.getKey();
            if (SpEvalTypeEnum.isAvgType(param.getTypeId())) {
                return count == 0 ? BigDecimal.ZERO : totalScore.divide(BigDecimal.valueOf(count), MC_TWO.getPrecision(), MC_TWO.getRoundingMode());
            } else {
                return totalScore;
            }
        }
        return BigDecimal.ZERO;
    }

    private Pair<Integer, BigDecimal> calcFormulaFastParamTotalScore(CalcResultRefContext refContext, FormulaFastParam param) {
        int count = 0;
        BigDecimal totalScore = BigDecimal.ZERO;
        for (FormulaFastParam.FastRefBO refId : param.getRefIds()) {
            if (refId.getRefType() == DimRuleCalcRefEnum.AOM_ACT.getCode()) {
                AomActvExtBO actvExt = refContext.aomActvMap.get(refId.getRefId());
                List<AomActvIndicatorDto> indicators = Optional.ofNullable(actvExt)
                    .map(AomActvExtBO::getIndicators).orElse(Lists.emptyList());
                for (AomActvIndicatorDto actvInd : indicators) {
                    count++;
                    totalScore = totalScore.add(nullAsZero(actvInd.getTotalScore()));
                }
            } else if (refId.getRefType() == DimRuleCalcRefEnum.IMPORT_DATA.getCode()) {
                XpdImportPO importPO = refContext.xpdImportMap.get(refId.getRefId());
                if (importPO != null) {
                    List<IndicatorDto> indicatorDtos = Optional.ofNullable(refContext.dimIndicatorsMap.get(importPO.getSdDimId()))
                        .map(Pair::getValue).orElse(Lists.emptyList());
                    count += indicatorDtos.size();
                    totalScore = totalScore.add(nullAsZero(importPO.getScoreTotal()).multiply(BigDecimal.valueOf(indicatorDtos.size())));
                }
            } else if (refId.getRefType() == DimRuleCalcRefEnum.PRI_PROFILE.getCode()) {
                count += refContext.modelIndicatorsMap.size();
                totalScore = totalScore.add(BigDecimal.TEN.multiply(BigDecimal.valueOf(refContext.modelIndicatorsMap.size())));
            }
        }
        return Pair.of(count, totalScore);
    }

    /**
     *
     * @param refContext
     * @param param
     * @param consumer
     * @return 是否中断处理
     */
    private boolean eachFormulaFastParamIndicator(CalcResultRefContext refContext, FormulaFastParam param, Predicate<FormulaRefIndicator> consumer) {
        return eachFormulaFastParamIndicator(refContext, param, null, consumer);
    }
    private boolean eachFormulaFastParamIndicator(CalcResultRefContext refContext, FormulaFastParam param,
        Map<String, Set<String>> fastRefIndMap, Predicate<FormulaRefIndicator> consumer) {
        int hasEmptyQty = 0;
        for (FormulaFastParam.FastRefBO refId : param.getRefIds()) {
            if (refId.getRefType() == DimRuleCalcRefEnum.AOM_ACT.getCode()) {
                AomActvExtBO actvExt = refContext.aomActvMap.getOrDefault(refId.getRefId(), new AomActvExtBO());
                List<AomActvIndicatorDto> indicators = Optional.ofNullable(actvExt.getIndicators()).orElse(Lists.emptyList());
                if (UacdTypeEnum.hasLevelActv(actvExt.getRefRegId())) {
                    indicators = indicators.stream().filter(indicator -> {
                        if (refContext.modelIndicatorsMap.get(indicator.getSdIndicatorId()) == null) {
                            return false;
                        }
                        IndicatorDto indicatorDto = refContext.usedIndicaotrInfoMap.get(indicator.getSdIndicatorId());
                        return indicatorDto == null || indicatorDto.getMaxLevel() <= 0
                               || Objects.equals(indicator.getStandardLevel(), indicatorDto.getStandardLevel());
                    }).collect(Collectors.toList());
                }
                if (fastRefIndMap != null) {
                    List<AomActvIndicatorDto> finalIndicators = indicators;
                    fastRefIndMap.computeIfAbsent(doneMainKey(refId.getRefType(), refId.getRefId()),
                        key -> finalIndicators.stream().map(AomActvIndicatorDto::getSdIndicatorId).collect(Collectors.toSet()));
                }
                if (IArrayUtils.hasMatch(indicators, indicator -> consumer.test(new FormulaRefIndicator(refId, indicator.getSdIndicatorId())))) {
                    hasEmptyQty++;
                }
            } else if (refId.getRefType() == DimRuleCalcRefEnum.IMPORT_DATA.getCode()) {
                XpdImportPO importPO = refContext.xpdImportMap.get(refId.getRefId());
                if (importPO != null) {
                    if (fastRefIndMap != null) {
                        fastRefIndMap.computeIfAbsent(doneMainKey(refId.getRefType(), refId.getRefId()),
                            key -> Optional.ofNullable(refContext.dimIndicatorsMap.get(importPO.getSdDimId())).map(Pair::getValue).orElse(Lists.newArrayList())
                                .stream().map(IndicatorDto::getItemId).collect(Collectors.toSet()));
                    }
                    if (IArrayUtils.hasMatch(Optional.ofNullable(refContext.dimIndicatorsMap.get(importPO.getSdDimId())).map(Pair::getValue).orElse(null),
                        indicatorDto -> consumer.test(new FormulaRefIndicator(refId, indicatorDto.getItemId())))) {
                        hasEmptyQty++;
                    }
                }
            } else if (refId.getRefType() == DimRuleCalcRefEnum.PRI_PROFILE.getCode()) {
                hasEmptyQty += eachFormulaFastParamIndicatorProfile(refContext, refId, consumer);
                if (fastRefIndMap != null) {
                    fastRefIndMap.computeIfAbsent(doneMainKey(refId.getRefType(), refId.getRefId()),
                        key -> refContext.modelIndicatorsMap.values().stream().map(IndicatorDto::getItemId).collect(Collectors.toSet()));
                }
            }
        }
        return hasEmptyQty > 0;
    }

    private int eachFormulaFastParamIndicatorProfile(CalcResultRefContext refContext,FormulaFastParam.FastRefBO refId, Predicate<FormulaRefIndicator> consumer) {
        int hasEmptyQty = 0;
        for (IndicatorDto indicatorDto : refContext.modelIndicatorsMap.values()) {
            if (consumer.test(new FormulaRefIndicator(refId, indicatorDto.getItemId()))) {
                hasEmptyQty++;
            }
        }
        return hasEmptyQty;
    }

    private List<XpdDimRuleIndicatorRefDto> formulaFixAddRefToContext(CalcResultRefContext refContext, ExpressionCalc formulaCalc) {
        if (formulaCalc == null) {
            return Lists.newArrayList();
        }
        Map<String, XpdDimRuleIndicatorRefDto> indicatorRefSet = new HashMap<>();
        Consumer<XpdDimRuleIndicatorRefDto> addIndicatorRefFunc = indicatorRef -> {
            String key = indicatorRef.getIndicatorId() + indicatorRef.getRefType() + CommonUtil.nullAsEmpty(indicatorRef.getRefId());
            indicatorRefSet.put(key, indicatorRef);
        };
        formulaCalc.paramChipList().forEach(paramChip -> {
            if (paramChip.getParamBean() == null) {
                return;
            }
            if (paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_EVAL.getCode()
                || paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_OTHER.getCode()) {
                FormulaAomActvParam param = (FormulaAomActvParam) paramChip.getParamBean();
                //AomActvExtBO actvExtBO = refContext.aomActvMap.get(param.getActvId());
                //if (actvExtBO != null && UacdTypeEnum.ACTV_PERF.getRegId().equals(actvExtBO.getRefRegId())) {
                //    //绩效活动只关联一个指标，这里优化下，这里使用最新的，应对绩效活动换了其他指标的情况
                //    Optional.ofNullable(IArrayUtils.getFirst(actvExtBO.getIndicators()))
                //        .ifPresent(item -> param.setSdIndicatorId(item.getSdIndicatorId()));
                //}
                refContext.addRefList(param.getSdIndicatorId(), false,
                    DimRuleCalcRefEnum.AOM_ACT.getCode(),
                    param.getActvId());
                addIndicatorRefFunc.accept(new XpdDimRuleIndicatorRefDto(param.getSdIndicatorId(),
                    DimRuleCalcRefEnum.AOM_ACT.getCode(), param.getActvId()));
            } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_IMPORT.getCode()) {
                FormulaImportParam param = (FormulaImportParam) paramChip.getParamBean();
                refContext.addRefList(
                    param.getSdIndicatorId(),false,
                    DimRuleCalcRefEnum.IMPORT_DATA.getCode(),
                    param.getImportId());
                addIndicatorRefFunc.accept(new XpdDimRuleIndicatorRefDto(param.getSdIndicatorId(),
                    DimRuleCalcRefEnum.IMPORT_DATA.getCode(), param.getImportId()));
            } else if (paramChip.getBizType() == FormulaTypeEnum.PRI_PROFILE.getCode()) {
                String sdIndicatorId = (String) paramChip.getParamBean();
                refContext.addRefList(
                    sdIndicatorId,false,
                    DimRuleCalcRefEnum.PRI_PROFILE.getCode(),
                    null);
                addIndicatorRefFunc.accept(new XpdDimRuleIndicatorRefDto(sdIndicatorId,
                    DimRuleCalcRefEnum.PRI_PROFILE.getCode(), null));
            } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_FAST.getCode()) {
                FormulaFastParam param = (FormulaFastParam) paramChip.getParamBean();
                eachFormulaFastParamIndicator(refContext, param, refContext.fastFormulaIndicatorMap, refInd -> {
                    refContext.addRefList(refInd.sdIndicatorId,false,
                        refInd.ref.getRefType(),
                        refInd.ref.getRefId());
                    addIndicatorRefFunc.accept(new XpdDimRuleIndicatorRefDto(refInd.sdIndicatorId,
                        refInd.ref.getRefType(), refInd.ref.getRefId()));
                    return false;
                });
            }
        });
        return indicatorRefSet.values().stream().toList();
    }

    private void prepareRefContext(CalcResultRefContext refContext) {
        log.info("{} prepareRefContext", refContext.logPrefix());
        Map<String, XpdImportPO> importIndicatorMap = new HashMap<>();
        refContext.xpdImportMap.forEach((key, item) -> {
            if (XpdImportTypeEnum.DIM_INDICATOR.getCode().equals(item.getImportType())) {
                importIndicatorMap.put(item.getId(), item);
            }
        });
        refContext.refIndicatorMap.forEach((key, ref) -> {
            if (ref.refType == DimRuleCalcRefEnum.AOM_ACT.getCode()) {
                AomActvExtBO actvMgr = refContext.aomActvMap.get(ref.refId);
                if (actvMgr != null) {
                    ref.refRegId = actvMgr.getRefRegId();
                    ref.indicatorMap.forEach((indKey, indInfo) -> {
                        AomActvIndicatorDto actvIndInfo = IArrayUtils.getFirstMatch(actvMgr.getIndicators(),
                            actvInd -> Objects.equals(actvInd.getSdIndicatorId(), indInfo.sdIndicatorId));
                        if (actvIndInfo != null) {
                            indInfo.totalScore = nullAsZero(actvIndInfo.getTotalScore());
                            indInfo.standardScore = actvIndInfo.getStandardScore();
                        } else {
                            indInfo.notExist = true;
                        }
                    });
                } else {
                    ref.notExist = true;
                }
            } else if (ref.refType == DimRuleCalcRefEnum.IMPORT_DATA.getCode()) {
                XpdImportPO importPO = importIndicatorMap.get(ref.refId);
                if (importPO != null) {
                    BigDecimal totalScore = nullAsZero(importPO.getScoreTotal());
                    ref.indicatorMap.forEach((sdIndId, indInfo) -> indInfo.totalScore = totalScore);
                } else {
                    ref.notExist = true;
                }
            } else if (ref.refType == DimRuleCalcRefEnum.PRI_PROFILE.getCode()) {
                ref.indicatorMap.forEach((sdIndId, indInfo) -> indInfo.totalScore = BigDecimal.TEN);
            } else {
                ref.notExist = true;
            }
        });
        //盘点结果：高级公式计算需要总分
        if (refContext.xpdRuleDto != null) {
            BigDecimal xpdRuleScoreTotal = formulaResult(refContext, refContext.xpdRuleDto.getFormulaCalc(), Pair.of(CALC_TOTAL_SCORE, null));
            refContext.xpdRuleDto.setCalcTotalScore(xpdRuleScoreTotal);
        }
    }

    public void refreshUserDimCombResult(String xpdId) {
        XpdPO xpd = xpdMapper.selectById(xpdId);
        if (xpd == null) {
            log.warn("LOG20963:{}", xpdId);
            return;
        }
        String orgId = xpd.getOrgId();
        CalcResultRefContext refContext = initCalcRefContext(orgId, xpdId, true);
        if (refContext == null) {
            log.warn("LOG20933:{} refreshUserDimCombResult refContext is null", xpdId);
            return;
        }
        String aomProjId = refContext.aomPrjId;
        List<String> allUserIds = rvActivityParticipationMemberRepo.findAllUserIdByActId(orgId, aomProjId);
        BatchOperationUtil.batchExecute(
            allUserIds, CALC_BATCH_COUNT, subUserIds -> {
                log.info("LOG20943:{}", refContext.logPrefix());
                calcDimCombUserResult(refContext, subUserIds);
            });
    }

    private static class DimRuleMetaDTO {
        private String xpdDimId;
        private String sdDimId;
        private BigDecimal savedScoreTotal;
        //是否导入覆盖
        private boolean coverByImport;
        private XpdDimRuleDto dimRuleBase;
        private List<DimRuleMetaDTO> subDimRules;
        private List<XpdDimRuleCalcDto> ruleCalcList;
        /**
         * 规则关联的指标，当前只有高级公式算了
         */
        private List<XpdDimRuleIndicatorRefDto> ruleIndicatorRefs;

        public boolean levelTypeAfterResult() {
            return DimLevelTypeEnum.needAfterResult(dimRuleBase.getLevelType());
        }
    }

    private class CalcResultRefContext {
        private String orgId;
        private String xpdId;
        private String aomPrjId;
        private String modelId;
        private int resultType;
        private int scoreSystem;
        private String gridId;
        private XpdCalcBatchDTO calcBatch;
        private List<DimRuleMetaDTO> allXpdDimList;
        private Map<String, XpdImportPO> xpdImportMap;
        private Map<String, AomActvExtBO> aomActvMap;
        private Map<String, Pair<ModelBase4Facade, List<IndicatorDto>>> dimIndicatorsMap;
        private Map<String, IndicatorDto> modelIndicatorsMap = new HashMap<>();
        private Map<String, List<DimRuleMetaDTO>> allDimListMap = new HashMap<>();
        //key:refId.refType
        private Map<String, RefIndicators> refIndicatorMap = new HashMap<>();
        private Map<String, IndicatorDto> usedIndicaotrInfoMap = new HashMap();
        private Set<String> usedCalcRuleIndicaotrSet = new HashSet<>();
        private Set<String> notUsedIndicatorIds = new HashSet<>();
        private Map<String, XpdDimRuleCalcDto> indicatorRuleCfgMap = new HashMap<>();
        private Map<String, Map<String, DimCalcUserResultDto>> refIndUserMap = new HashMap<>();
        private Map<String, Set<String>> refDoneUserMap = new HashMap<>();
        private List<XpdResultUserIndicatorPO> userIndicatorList = new ArrayList<>();
        private List<XpdResultUserDimPO> userDimList = new ArrayList<>();
        /**
         * key doneMainKey()
         */
        private Map<String, Set<String>> fastFormulaIndicatorMap = new HashMap<>();

        private XpdRuleMainDto xpdRuleDto;
        private List<XpdRuleLevelDto> xpdRuleLevels;
        private List<XpdRuleCalcDimPO> xpdCalcDimRules;
        private List<XpdRuleCalcIndicatorDto> xpdCalcIndRules;
        private List<XpdDimRuleIndicatorRefDto> xpdRuleIndicatorRefs;

        // 宫格相关缓存
        private XpdGridPO xpdGrid;
        private GridCoordinateEnum gridCoordinate;
        private Map<String, Integer> gridLevelOrderMap = new HashMap<>();
        private Map<String, XpdGridCellPO> gridCellMap = new HashMap<>();
        private List<XpdDimCombPO> dimCombList = new ArrayList<>();
        public RefIndicatorInfo queryIndicatorInfo(int refType, String refId, String sdIndicatorId) {
            return Optional.ofNullable(refIndicatorMap.get(indicatorInfoKey(refType, refId)))
                .map(RefIndicators::getIndicatorMap).map(indMap -> indMap.get(sdIndicatorId)).orElse(null);
        }

        public XpdResultUserDimPO newResultUserDimPO(String sdDimId, String userId) {
            XpdResultUserDimPO userDimPO = new XpdResultUserDimPO();
            userDimPO.initData(null);
            userDimPO.setOrgId(orgId);
            userDimPO.setXpdId(xpdId);
            userDimPO.setUserId(userId);
            userDimPO.setSdDimId(sdDimId);
            userDimPO.setCalcBatchNo(calcBatch.getBatchNo());
            userDimPO.setScoreValue(BigDecimal.ZERO);
            userDimPO.setQualifiedPtg(BigDecimal.ZERO);
            return userDimPO;
        }

        public XpdResultUserIndicatorPO newResultUserIndicatorPO(String userId) {
            XpdResultUserIndicatorPO userIndResult = new XpdResultUserIndicatorPO();
            userIndResult.initData(null);
            userIndResult.setOrgId(orgId);
            userIndResult.setXpdId(xpdId);
            userIndResult.setUserId(userId);
            userIndResult.setCalcBatchNo(calcBatch.getBatchNo());
            return userIndResult;
        }

        public XpdResultUserPO newUserResult(String userId, BigDecimal scoreValue, BigDecimal qualifiedPtg) {
            XpdResultUserPO result = new XpdResultUserPO();
            result.initData(null);
            result.setOrgId(orgId);
            result.setXpdId(xpdId);
            result.setUserId(userId);
            result.setCalcBatchNo(calcBatch.getBatchNo());
            result.setScoreValue(nullAsZero(scoreValue));
            result.setQualifiedPtg(nullAsZero(qualifiedPtg));
            result.setCompetent(YesOrNo.NO.getValue());
            return result;
        }

        /**
         * 初始化宫格相关缓存
         */
        public void initGridCache() {

            // 获取宫格维度
            dimCombList = xpdDimCombMapper.listByXpdId(orgId, xpdId);

            // 获取宫格配置
            XpdRuleConfPO xpdRuleConf = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
            if (xpdRuleConf == null) {
                log.warn("LOG20843:initGridCache failed, cannot get xpdRuleConf for xpdId={}", xpdId);
                return;
            }

            // 获取宫格信息
            xpdGrid = xpdGridMapper.selectByPrimaryKey(xpdRuleConf.getGridId());
            if (xpdGrid == null) {
                log.warn("LOG20853:initGridCache failed, cannot get xpdGrid for gridId={}", xpdRuleConf.getGridId());
                return;
            }

            // 获取坐标枚举
            gridCoordinate = GridCoordinateEnum.getByGridType(xpdGrid.getGridType());

            // 获取宫格分层信息
            List<XpdGridLevelPO> gridLevels = xpdGridLevelMapper.listByGridId(orgId, xpdGrid.getId());
            gridLevelOrderMap = gridLevels.stream()
                .collect(Collectors.toMap(XpdGridLevelPO::getId, XpdGridLevelPO::getOrderIndex));

            // 获取格子信息
            List<XpdGridCellPO> gridCells = xpdGridCellMapper.listByGridId(orgId, xpdGrid.getId());
            gridCellMap = gridCells.stream()
                .collect(Collectors.toMap(
                    cell -> buildCellKey(cell.getXIndex(), cell.getYIndex(), cell.getDimCombId()),
                    Function.identity()));
        }

        // 根据宫格配置的不同，构建不同的格子key
        @Nonnull
        private String buildCellKey(Integer xIndex, Integer yIndex, String dimCombId) {
            return this.xpdGrid.getConfigType() != null && this.xpdGrid.getConfigType() == 0 ? xIndex + "_" + yIndex :
                dimCombId + "_" + xIndex + "_" + yIndex;
        }

        /**
         * 根据坐标获取格子
         */
        public XpdGridCellPO getGridCell(String dimCombId, Integer xIndex, Integer yIndex) {
            if (xIndex == null || yIndex == null) {
                return null;
            }
            return gridCellMap.get(buildCellKey(xIndex, yIndex, dimCombId));
        }

        public boolean calcDimByScore() {
            return resultType == DimResultTypeEnum.SCORE_VALUE.getCode();
        }

        public void addRefList(String sdIndicatorId, boolean genIndicatorResult, int refType, String refId) {
            if (StringUtils.isNotEmpty(sdIndicatorId)) {
                XpdDimRuleCalcRefDto ruleCalcRefDto = new XpdDimRuleCalcRefDto();
                ruleCalcRefDto.setRefType(refType);
                ruleCalcRefDto.setRefId(refId);
                addRefList(sdIndicatorId, genIndicatorResult, Lists.newArrayList(ruleCalcRefDto));
            }
        }
        public void addRefList(String sdIndicatorId, boolean genIndicatorResult, List<XpdDimRuleCalcRefDto> refList) {
            if (refList != null && StringUtils.isNotEmpty(sdIndicatorId)) {
                for (XpdDimRuleCalcRefDto ruleCalcRefDto : refList) {
                    String refKey = indicatorInfoKey(ruleCalcRefDto.getRefType(), ruleCalcRefDto.getRefId());
                    RefIndicators refIndicators = refIndicatorMap.computeIfAbsent(refKey, key -> {
                        RefIndicators mapVal = new RefIndicators();
                        mapVal.refId = ruleCalcRefDto.getRefId();
                        mapVal.refType = ruleCalcRefDto.getRefType();
                        mapVal.indicatorMap = new HashMap<>();
                        return mapVal;
                    });
                    RefIndicatorInfo indicatorInfo = new RefIndicatorInfo();
                    indicatorInfo.sdIndicatorId = sdIndicatorId;
                    refIndicators.indicatorMap.put(sdIndicatorId, indicatorInfo);
                    usedIndicaotrInfoMap.put(sdIndicatorId, null);
                    if (genIndicatorResult) {
                        usedCalcRuleIndicaotrSet.add(sdIndicatorId);
                    }
                }
            }
        }

        public void resetUserResult() {
            refIndUserMap.forEach((key, userResultMap) -> userResultMap.clear());
            refDoneUserMap.clear();
            userIndicatorList.clear();
            userDimList.clear();
        }

        public void addRefUserResult(int refType, String refId, String sdIndicatorId, DimCalcUserResultDto userResult) {
            String mainKey = mainKey(refType, refId, sdIndicatorId);
            Map<String, DimCalcUserResultDto> resultMap = refIndUserMap.computeIfAbsent(mainKey, key -> new HashMap<>());
            resultMap.put(userResult.getUserId(), userResult);
        }

        public DimCalcUserResultDto queryAomUserResult(CalcResultRefContext refContext, String aomActvId, String sdIndicatorId, String userId) {
            if (!refContext.aomActvMap.containsKey(aomActvId)) {
                //活动不存在了，按0处理
                return new DimCalcUserResultDto(BigDecimal.ZERO);
            }
            Set<String> doneUserIds = Optional.ofNullable(refDoneUserMap.get(doneMainKey(DimRuleCalcRefEnum.AOM_ACT.getCode(), aomActvId)))
                .orElse(emptySet);
            if (!doneUserIds.contains(userId)) {
                //活动未完成固定返回null
                return null;
            }
            String mainKey = mainKey(DimRuleCalcRefEnum.AOM_ACT.getCode(), aomActvId, sdIndicatorId);
            return Optional.ofNullable(refIndUserMap.get(mainKey)).map(map -> map.get(userId))
                .orElse(new DimCalcUserResultDto(BigDecimal.ZERO));
        }

        public DimCalcUserResultDto queryUserResult(int refType, String refId, String sdIndicatorId, String userId) {
            String mainKey = mainKey(refType, refId, sdIndicatorId);
            return Optional.ofNullable(refIndUserMap.get(mainKey)).map(map -> map.get(userId))
                    .orElse(new DimCalcUserResultDto(BigDecimal.ZERO));
        }

        public String logPrefix() {
            return String.format("orgId %s xpdId %s batchNo %s", orgId, xpdId, Optional.ofNullable(calcBatch).map(XpdCalcBatchDTO::getBatchNo).orElse(null));
        }
    }

    private static String mainKey(int refType, String refId, String sdIndicatorId) {
        if (refType == DimRuleCalcRefEnum.PRI_PROFILE.getCode()) {
            refId = StringPool.EMPTY;
        }
        return sdIndicatorId + StringPool.DOT + refId + StringPool.DOT + refType;
    }
    private static String doneMainKey(int refType, String refId) {
        if (refType == DimRuleCalcRefEnum.PRI_PROFILE.getCode()) {
            refId = StringPool.EMPTY;
        }
        return refType + StringPool.DOT + refId;
    }
    private static String indicatorInfoKey(int refType, String refId) {
        if (refType == DimRuleCalcRefEnum.PRI_PROFILE.getCode()) {
            refId = StringPool.EMPTY;
        }
        return refType + StringPool.DOT + refId;
    }
    private static BigDecimal nullAsZero(BigDecimal val) {
        return val == null ? BigDecimal.ZERO : val;
    }
    private static <T> BigDecimal nullAsZero(T valBean, Function<T, BigDecimal> valGetter) {
        return Optional.ofNullable(valBean).map(valGetter).orElse(BigDecimal.ZERO);
    }

    private class RefIndicators {
        private String refId;
        //参考 DimRuleCalcRefEnum
        private int refType;
        /**
         * 对应枚举值 UacdTypeEnum
         */
        private String refRegId;
        private boolean notExist = true;
        //key: sdIndicatorId
        private Map<String, RefIndicatorInfo> indicatorMap;

        public Map<String, RefIndicatorInfo> getIndicatorMap() {
            return indicatorMap;
        }
    }

    private class RefIndicatorInfo {
        private String sdIndicatorId;
        private BigDecimal totalScore;
        private BigDecimal standardScore;
        private boolean notExist = true;

        public String getSdIndicatorId() {
            return sdIndicatorId;
        }

        public BigDecimal getTotalScore() {
            return totalScore;
        }

        public BigDecimal getStandardScore() {
            return standardScore;
        }
    }

    private class IndicatorResult {
        private boolean hasNoResult;
        private BigDecimal score;
        private boolean qualified;
        private List<DimUserIndicatorResultDto> resultList = new ArrayList<>();
    }

    private class CalcDimResultParam {
        private boolean addIndResult;
        //是：按得分算,否：按达标率算
        private boolean byScore;

        public CalcDimResultParam(boolean addIndResult, boolean byScore) {
            this.addIndResult = addIndResult;
            this.byScore = byScore;
        }
    }

    private class DimResult {
        private boolean hasNoResult;
        private BigDecimal score;
        private BigDecimal qualifiedPtg;

        public boolean isHasNoResult() {
            return hasNoResult;
        }

        public BigDecimal getScore() {
            return score;
        }

        public BigDecimal getQualifiedPtg() {
            return qualifiedPtg;
        }
    }

    private class FormulaRefIndicator {
        private FormulaFastParam.FastRefBO ref;
        private String sdIndicatorId;

        public FormulaRefIndicator(FormulaFastParam.FastRefBO ref, String sdIndicatorId) {
            this.ref = ref;
            this.sdIndicatorId = sdIndicatorId;
        }
    }

    private class UserIndicatorResults {
        private String userId;
        private String sdIndicatorId;
        private List<DimUserIndicatorResultDto> resultList = new ArrayList<>();
    }

    private class CaliUserResults {
        private Map<String, CaliUpdateUserResultReq> indResultMap;
        private Map<String, CaliDimResultBean> dimResultMap;
        //key: mainKey
        private Map<String, DimCalcUserResultDto> refResultMap = new HashMap<>();
        private Set<String> aomDoneRefIds;
        //key: indicatorId
        private Map<String, BigDecimal> directScoreMap;

        public BigDecimal aomResult(String actvId, String sdIndicatorId, String typeId) {
            BigDecimal score = directScoreMap.get(sdIndicatorId);
            if (score != null) {
                return score;
            }
            if (!aomDoneRefIds.contains(actvId)) {
                return null;
            }
            DimCalcUserResultDto resultDto = refResultMap.get(mainKey(DimRuleCalcRefEnum.AOM_ACT.getCode(), actvId, sdIndicatorId));
            if ( StringUtils.isEmpty(typeId) || !NumberUtil.isNumber(typeId)) {
                //非测评结果
                score = resultDto.getScoreValue();
            } else {
                //测评结果
                score = SpEvalTypeEnum.evalTypeValue(typeId, resultDto);
            }
            return nullAsZero(score);
        }

        public BigDecimal normalResult(int refType, String refId, String sdIndicatorId) {
            BigDecimal score = directScoreMap.get(sdIndicatorId);
            if (score != null) {
                return score;
            }
            score = Optional.ofNullable(refResultMap.get(mainKey(refType, refId, sdIndicatorId)))
                .map(DimCalcUserResultDto::getScoreValue).orElse(null);
            return nullAsZero(score);
        }
    }

    public void preHandleCaliResult(int resultType, List<CaliUpdateUserResultReq> resultList) {
        IArrayUtils.remove(resultList, item -> StringUtils.isEmpty(item.getCaliVal()));
        resultList.forEach(item -> {
            if (resultType == CaliResultTypeEnum.TYPE_DIM_LEVEL.getType()) {
                item.setGridLevelId(item.getCaliVal());
            } else if (resultType == CaliResultTypeEnum.TYPE_DIM_SCORE.getType()
                       || resultType == CaliResultTypeEnum.TYPE_INDICATOR_SCORE.getType()) {
                item.setScoreValue(new BigDecimal(item.getCaliVal()));
            } else if (resultType == CaliResultTypeEnum.TYPE_QUALIFIED_PTG.getType()) {
                item.setQualifiedPtg(new BigDecimal(item.getCaliVal()));
            } else if (resultType == CaliResultTypeEnum.TYPE_QUALIFIED.getType()) {
                item.setQualified(Integer.valueOf(item.getCaliVal()));
            }
        });
    }
}
