package com.yxt.talent.rv.application.calimeet;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.repo.RedisRepository;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.export.DlcComponent;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.calimeet.dto.*;
import com.yxt.talent.rv.application.calimeet.impt.CaliDimLevelTemExportStrategy;
import com.yxt.talent.rv.application.calimeet.impt.CaliDimTemExportStrategy;
import com.yxt.talent.rv.application.calimeet.impt.CaliIndicatorTemExportStrategy;
import com.yxt.talent.rv.application.calimeet.impt.DlcExportWrapper;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.CaliResultTypeEnum;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimCalcTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DownInfoUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserIndicatorPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleConfPO;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.ubiz.export.bean.ExportFileInfo;
import com.yxt.ubiz.export.common.enums.ExportFileTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class CaliMeetImportService {
    private final DlcComponent dlcComponent;
    private final CaliDimLevelTemExportStrategy caliDimLevelTemExportStrategy;
    private final CaliDimTemExportStrategy caliDimTemExportStrategy;
    private final CaliIndicatorTemExportStrategy caliIndicatorTemExportStrategy;
    private final I18nComponent i18nComponent;
    private final DlcExportWrapper dlcExportWrapper;
    private final AuthService authService;
    private final CalimeetUserMapper calimeetUserMapper;
    private final CalimeetMapper calimeetMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final CalimeetResultUserDimMapper calimeetResultUserDimMapper;
    private final CalimeetResultUserIndicatorMapper calimeetResultUserIndicatorMapper;
    private final XpdRuleConfMapper xpdRuleConfMapper;
    private final XpdResultCalcService xpdResultCalcService;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final RedisRepository talentRedisRepository;
    private final CalimeetRecordMapper calimeetRecordMapper;

    /**
     * 下载指标结果模板
     * @param currentUser
     * @param calId
     */
    public void downloadTemIndicator(UserCacheDetail currentUser, String calId, ImportUserDTO importUserDTO) {
        List<CaliUserDTO> users = getCaliMeetUsers(currentUser.getOrgId(), calId, importUserDTO);
//        List<UdpLiteUserPO> users = udpLiteUserMapper.selectByUserIds(currentUser.getOrgId(), uids);
        if (CollectionUtils.isEmpty(users)){
            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_USER);
        }
        CaliTempExportBean caliTempExportBean = new CaliTempExportBean();
        CalimeetPO calimeetPO = getCalimeetPO(currentUser.getOrgId(), calId);

        XpdRuleConfPO xpdRuleConfPO = xpdRuleConfMapper.selectByXpdId(calimeetPO.getOrgId(), calimeetPO.getXpdId());
        if (xpdRuleConfPO == null){
            throw new ApiException(ExceptionKeys.XPD_RULE_CONF_NOT_EXIST);
        }
        caliTempExportBean.setResultType(xpdRuleConfPO.getResultType());

        // 获取维度指标列表
        LinkedHashMap<String, List<CaliIndicatorDTO>> dmIndicators = getDmIndicator(currentUser.getOrgId(), calimeetPO.getXpdId());
        if (dmIndicators.isEmpty()){
            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_DIMS);
        }
        caliTempExportBean.setDmIndicators(dmIndicators);
        List<String> userIds = StreamUtil.mapList(users, CaliUserDTO::getUserId);
        List<CaliUserDimDTO> userDims = getUserDims(currentUser.getOrgId(), calimeetPO.getXpdId(), calId, userIds, 3, xpdRuleConfPO);
//        if (CollectionUtils.isEmpty(userDims)){
//            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_RESULT);
//        }

        Map<String,List<CaliUserDimDTO>> userDimMap = userDims.stream().collect(Collectors.groupingBy(CaliUserDimDTO::getUserId));

        List<CaliUserImportDTO> userDatas = new ArrayList<>();

        // 遍历人员，插入人员对应的校准前数据
        for (CaliUserDTO u : users){
            CaliUserImportDTO user = new CaliUserImportDTO();
            user.setUserId(u.getUserId());
            user.setUserName(u.getUsername());
            user.setFullName(u.getFullname());

            if (userDimMap.containsKey(u.getUserId())) {
                List<CaliUserDimDTO> userDim = userDimMap.get(u.getUserId());
                Map<String, String> userDimResult =
                    StreamUtil.list2map(userDim, CaliUserDimDTO::getIndicatorId, CaliUserDimDTO::getResult);
                Map<String, String> dmResults = new LinkedHashMap<>();
                dmIndicators.forEach((k,v) -> {
                    for (CaliIndicatorDTO v1 : v) {
                        String key = v1.getIndicatorId(); // 维度+指标的组合作为key
                        if (!userDimResult.isEmpty() && userDimResult.containsKey(key)) {
                            dmResults.put(key, Optional.ofNullable(userDimResult.get(key)).orElse(""));
                        }
                    }
                });
                user.setDmResults(dmResults);
            }

            // 校准结果添加
            CaliUpdateUserResultWrapDto caliUpdateUserResultWrapDto = u.getCalimeetDimResultDto();
            if (caliUpdateUserResultWrapDto != null){
                // 指标处理
                List<CaliUpdateUserResultDto> userResults = caliUpdateUserResultWrapDto.getUserResults();
                Map<String, String> dmCaliResults = new LinkedHashMap<>();
                if (caliUpdateUserResultWrapDto.getResultType() == CaliResultTypeEnum.TYPE_INDICATOR_SCORE.getType()){
                    userResults.forEach(ur -> {
                        if (ur.getScoreValue() != null){
                            dmCaliResults.put(ur.getSdIndicatorId(), ur.getScoreValue().stripTrailingZeros().toPlainString());
                        }
                    });
                }
                if (caliUpdateUserResultWrapDto.getResultType() == CaliResultTypeEnum.TYPE_QUALIFIED.getType()){
                    userResults.forEach(ur -> {
                        if (ur.getQualified() != null){
                            dmCaliResults.put(ur.getSdIndicatorId(), ur.getQualified() == 1 ? "达标" : "不达标");
                        }
                    });
                }
                user.setDmCaliResults(dmCaliResults);
            }
            user.setReason(u.getReason());
            user.setRecommend(u.getSuggestion());

            userDatas.add(user);
        }
        caliTempExportBean.setUserDatas(userDatas);

        String fileName = i18nComponent.getI18nValue(CaliIndicatorTemExportStrategy.TASK_NAME);


        ExportFileInfo exportFileInfo = new ExportFileInfo();
        //设置下载信息
        exportFileInfo.setDownInfo(DownInfoUtil.getDownInfo(AppConstants.FUNCTION_NAME));
        exportFileInfo.setLocale(authService.getLocale());
        exportFileInfo.setFileType(ExportFileTypeEnum.EXCEL);
        //不需要后缀.后缀由fileType的suffix决定,此文件名必须唯一
        exportFileInfo.setName(fileName);
        exportFileInfo.setFileName(fileName + System.nanoTime());
        exportFileInfo.setOrgId(currentUser.getOrgId());
        exportFileInfo.setUserId(currentUser.getUserId());
        exportFileInfo.setFullname(currentUser.getFullname());
        exportFileInfo.setDownloadI18n(true);
        dlcExportWrapper.export(exportFileInfo, () -> dlcComponent.upload2TemporaryDisk(exportFileInfo.getFileName() + ExportFileTypeEnum.EXCEL.getSuffix(), caliTempExportBean, caliIndicatorTemExportStrategy));
    }

    /**
     * 下载维度分层模板
     * @param currentUser
     * @param calId
     */
    public void downloadTemDimLevel(UserCacheDetail currentUser, String calId, ImportUserDTO importUserDTO) {
        List<CaliUserDTO> users = getCaliMeetUsers(currentUser.getOrgId(), calId, importUserDTO);
//        List<UdpLiteUserPO> users = udpLiteUserMapper.selectByUserIds(currentUser.getOrgId(), uids);
        if (CollectionUtils.isEmpty(users)){
            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_USER);
        }
        CalimeetPO calimeetPO = getCalimeetPO(currentUser.getOrgId(), calId);
        CaliTempExportBean caliTempExportBean = new CaliTempExportBean();
        XpdRuleConfPO xpdRuleConfPO = xpdRuleConfMapper.selectByXpdId(calimeetPO.getOrgId(), calimeetPO.getXpdId());
        if (xpdRuleConfPO == null){
            throw new ApiException(ExceptionKeys.XPD_RULE_CONF_NOT_EXIST);
        }
        caliTempExportBean.setResultType(xpdRuleConfPO.getResultType());
        // 获取维度列表
        List<CaliDmDTO> dmsPOs = getDms(currentUser.getOrgId(), calimeetPO.getXpdId(), false);
        if (CollectionUtils.isEmpty(dmsPOs)){
            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_DIMS);
        }
        List<String> dms = StreamUtil.mapList(dmsPOs, CaliDmDTO::getDmName);
        caliTempExportBean.setDms(dmsPOs);
        List<String> userIds = StreamUtil.mapList(users, CaliUserDTO::getUserId);
        List<CaliUserDimDTO> userDims = getUserDims(currentUser.getOrgId(),calimeetPO.getXpdId(), calId, userIds, 2, xpdRuleConfPO);
//        if (CollectionUtils.isEmpty(userDims)){
//            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_RESULT);
//        }
        List<XpdLevelAggDTO> levels = getLevels(currentUser.getOrgId(), calimeetPO.getXpdId());
        Map<String, String> levelMap = StreamUtil.list2map(levels, XpdLevelAggDTO::getLevelId, XpdLevelAggDTO::getLevelName);

        Map<String,List<CaliUserDimDTO>> userDimMap = userDims.stream().collect(Collectors.groupingBy(CaliUserDimDTO::getUserId));

        List<CaliUserImportDTO> userDatas = new ArrayList<>();

        // 遍历人员，插入人员对应的校准前数据
        for (CaliUserDTO u : users){
            CaliUserImportDTO user = new CaliUserImportDTO();
            user.setUserId(u.getUserId());
            user.setUserName(u.getUsername());
            user.setFullName(u.getFullname());

            if (userDimMap.containsKey(u.getUserId())) {
                List<CaliUserDimDTO> userDim = userDimMap.get(u.getUserId());
                Map<String, String> userDimResult =
                    StreamUtil.list2map(userDim, CaliUserDimDTO::getDim, CaliUserDimDTO::getResult);
                Map<String, String> dmResults = new LinkedHashMap<>();
                for (String dm : dms) {
                    if (!userDimResult.isEmpty() && userDimResult.containsKey(dm)) {
                        dmResults.put(dm, Optional.ofNullable(userDimResult.get(dm)).orElse(""));
                    }
                }
                user.setDmResults(dmResults);
            }

            CaliUpdateUserResultWrapDto caliUpdateUserResultWrapDto = u.getCalimeetDimResultDto();
            if (caliUpdateUserResultWrapDto != null){
                // 维度分层处理
                List<CaliUpdateUserResultDto> userResults = caliUpdateUserResultWrapDto.getUserResults();
                Map<String, String> dmCaliResults = new LinkedHashMap<>();
                if (caliUpdateUserResultWrapDto.getResultType() == CaliResultTypeEnum.TYPE_DIM_LEVEL.getType()){
                    userResults.forEach(ur -> {
                        if (ur.getGridLevelId() != null && levelMap.containsKey(ur.getGridLevelId())){
                            dmCaliResults.put(ur.getSdDimId(), levelMap.get(ur.getGridLevelId()));
                        }
                    });
                }
                user.setDmCaliResults(dmCaliResults);
            }
            user.setReason(u.getReason());
            user.setRecommend(u.getSuggestion());
            userDatas.add(user);
        }

        caliTempExportBean.setUserDatas(userDatas);


        String fileName = i18nComponent.getI18nValue(CaliDimLevelTemExportStrategy.TASK_NAME);
        ExportFileInfo exportFileInfo = new ExportFileInfo();
        //设置下载信息
        exportFileInfo.setDownInfo(DownInfoUtil.getDownInfo(AppConstants.FUNCTION_NAME));
        exportFileInfo.setLocale(authService.getLocale());
        exportFileInfo.setFileType(ExportFileTypeEnum.EXCEL);
        //不需要后缀.后缀由fileType的suffix决定,此文件名必须唯一
        exportFileInfo.setName(fileName);
        exportFileInfo.setFileName(fileName + System.nanoTime());
        exportFileInfo.setOrgId(currentUser.getOrgId());
        exportFileInfo.setUserId(currentUser.getUserId());
        exportFileInfo.setFullname(currentUser.getFullname());
        exportFileInfo.setDownloadI18n(true);
        dlcExportWrapper.export(exportFileInfo, () -> dlcComponent.upload2TemporaryDisk(exportFileInfo.getFileName() + ExportFileTypeEnum.EXCEL.getSuffix(), caliTempExportBean, caliDimLevelTemExportStrategy));
    }

    /**
     * 下载维度结果模板
     * @param currentUser
     * @param calId
     */
    public void downloadTemDim(UserCacheDetail currentUser, String calId, ImportUserDTO importUserDTO) {
        List<CaliUserDTO> users = getCaliMeetUsers(currentUser.getOrgId(), calId, importUserDTO);
//        List<UdpLiteUserPO> users = udpLiteUserMapper.selectByUserIds(currentUser.getOrgId(), uids);
        if (CollectionUtils.isEmpty(users)){
            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_USER);
        }
        CaliTempExportBean caliTempExportBean = new CaliTempExportBean();
        CalimeetPO calimeetPO = getCalimeetPO(currentUser.getOrgId(), calId);
        XpdRuleConfPO xpdRuleConfPO = xpdRuleConfMapper.selectByXpdId(calimeetPO.getOrgId(), calimeetPO.getXpdId());
        if (xpdRuleConfPO == null){
            throw new ApiException(ExceptionKeys.XPD_RULE_CONF_NOT_EXIST);
        }
        caliTempExportBean.setResultType(xpdRuleConfPO.getResultType());
        List<CaliDmDTO> dmsPOs = getDms(currentUser.getOrgId(), calimeetPO.getXpdId(), true);
        if (CollectionUtils.isEmpty(dmsPOs)){
            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_DIMS);
        }
        List<String> dms = StreamUtil.mapList(dmsPOs, CaliDmDTO::getDmName);
        if (CollectionUtils.isEmpty(dms)){
            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_DIMS);
        }
        caliTempExportBean.setDms(dmsPOs);
        List<String> userIds = StreamUtil.mapList(users, CaliUserDTO::getUserId);
        List<CaliUserDimDTO> userDims = getUserDims(currentUser.getOrgId(),calimeetPO.getXpdId(), calId, userIds,1, xpdRuleConfPO);
//        if (CollectionUtils.isEmpty(userDims)){
//            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_RESULT);
//        }

        Map<String,List<CaliUserDimDTO>> userDimMap = userDims.stream().collect(Collectors.groupingBy(CaliUserDimDTO::getUserId));

        List<CaliUserImportDTO> userDatas = new ArrayList<>();

        // 遍历人员，插入人员对应的校准前数据
        for (CaliUserDTO u : users){
            CaliUserImportDTO user = new CaliUserImportDTO();
            user.setUserId(u.getUserId());
            user.setUserName(u.getUsername());
            user.setFullName(u.getFullname());

            if (userDimMap.containsKey(u.getUserId())) {
                List<CaliUserDimDTO> userDim = userDimMap.get(u.getUserId());
                Map<String, String> userDimResult =
                    StreamUtil.list2map(userDim, CaliUserDimDTO::getDim, CaliUserDimDTO::getResult);
                Map<String, String> dmResults = new LinkedHashMap<>();
                for (String dm : dms) {
                    if (!userDimResult.isEmpty() && userDimResult.containsKey(dm)) {
                        dmResults.put(dm, Optional.ofNullable(userDimResult.get(dm)).orElse(""));
                    }
                }
                user.setDmResults(dmResults);
            }

            // 校准结果添加
            CaliUpdateUserResultWrapDto caliUpdateUserResultWrapDto = u.getCalimeetDimResultDto();
            if (caliUpdateUserResultWrapDto != null){
                // 维度处理
                List<CaliUpdateUserResultDto> userResults = caliUpdateUserResultWrapDto.getUserResults();
                Map<String, String> dmCaliResults = new LinkedHashMap<>();
                if (caliUpdateUserResultWrapDto.getResultType() == CaliResultTypeEnum.TYPE_DIM_SCORE.getType()){
                    userResults.forEach(ur -> {
                        if (ur.getScoreValue() != null){
                            dmCaliResults.put(ur.getSdDimId(), ur.getScoreValue().stripTrailingZeros().toPlainString());
                        }
                    });
                }
                if (caliUpdateUserResultWrapDto.getResultType() == CaliResultTypeEnum.TYPE_QUALIFIED_PTG.getType()){
                    userResults.forEach(ur -> {
                        if (ur.getQualifiedPtg() != null){
                            dmCaliResults.put(ur.getSdDimId(), ur.getQualifiedPtg().stripTrailingZeros().toPlainString() + "%");
                        }
                    });
                }
                user.setDmCaliResults(dmCaliResults);
            }
            user.setReason(u.getReason());
            user.setRecommend(u.getSuggestion());

            userDatas.add(user);
        }

        caliTempExportBean.setUserDatas(userDatas);

        String fileName = i18nComponent.getI18nValue(CaliDimTemExportStrategy.TASK_NAME);
        ExportFileInfo exportFileInfo = new ExportFileInfo();
        //设置下载信息
        exportFileInfo.setDownInfo(DownInfoUtil.getDownInfo(AppConstants.FUNCTION_NAME));
        exportFileInfo.setLocale(authService.getLocale());
        exportFileInfo.setFileType(ExportFileTypeEnum.EXCEL);
        //不需要后缀.后缀由fileType的suffix决定,此文件名必须唯一
        exportFileInfo.setName(fileName);
        exportFileInfo.setFileName(fileName + System.nanoTime());
        exportFileInfo.setOrgId(currentUser.getOrgId());
        exportFileInfo.setUserId(currentUser.getUserId());
        exportFileInfo.setFullname(currentUser.getFullname());
        exportFileInfo.setDownloadI18n(true);
        dlcExportWrapper.export(exportFileInfo, () -> dlcComponent.upload2TemporaryDisk(exportFileInfo.getFileName() +ExportFileTypeEnum.EXCEL.getSuffix(), caliTempExportBean, caliDimTemExportStrategy));
    }

    private LinkedHashMap<String, List<CaliIndicatorDTO>> getDmIndicator(String orgId, String xpdId) {
        //6.3: 获取维度指标列表
        LinkedHashMap<String, List<CaliIndicatorDTO>> dmIndicators = new LinkedHashMap<>();
        XpdCaliCalcFullDto fullDto = xpdResultCalcService.buildXpdCalcFormula(orgId, xpdId);
        if (fullDto == null){
            return dmIndicators;
        }
        Map<String, String> sdDimNameMap = fullDto.getSdDimNameMap();
        if (sdDimNameMap.isEmpty()){
            return dmIndicators;
        }
        Map<String, XpdSdIndicatorScoreDto> sdIndicatorNameMap = fullDto.getSdIndicatorMap();
        if (sdIndicatorNameMap.isEmpty()){
            return dmIndicators;
        }
        List<XpdDimRule4Cali> ruleList = fullDto.getRuleList().stream().filter(rule->
            rule.getCaliCalcType() != XpdDimRule4Cali.TYPE_NONE
                && !DimCalcTypeEnum.isPerf(rule.getCalcType())
                && CollectionUtils.isNotEmpty(rule.getFormulaSdIndicatorSet())
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ruleList)){
            return dmIndicators;
        }
        for (XpdDimRule4Cali rule : ruleList) {
            String dimId = rule.getSdDimId();
            if (!sdDimNameMap.containsKey(dimId)){
                continue;
            }
            String dmName = sdDimNameMap.get(dimId);

            List<CaliIndicatorDTO> indicators = new ArrayList<>();
            if (CollectionUtils.isEmpty(rule.getFormulaSdIndicatorSet())){
                continue;
            }

            for (String indicatorId : rule.getFormulaSdIndicatorSet()){
                if (sdIndicatorNameMap.containsKey(indicatorId)){
                    CaliIndicatorDTO caliIndicatorDTO = new CaliIndicatorDTO();
                    caliIndicatorDTO.setIndicatorId(indicatorId);
                    String indicatorNo = sdIndicatorNameMap.get(indicatorId).getNum() != null ? "["+sdIndicatorNameMap.get(indicatorId).getNum()+"]" : "";
                    caliIndicatorDTO.setIndicatorName(sdIndicatorNameMap.get(indicatorId).getName()+indicatorNo);
                    caliIndicatorDTO.setTotalScore(sdIndicatorNameMap.get(indicatorId).getTotalScore());
                    indicators.add(caliIndicatorDTO);
                }
            }
            if (CollectionUtils.isNotEmpty(indicators)){
                dmIndicators.put(dmName, indicators);
            }
        }
        return dmIndicators;
    }

    private List<CaliUserDimDTO> getUserDims(String orgId, String xpdId, String calId, List<String> userIds, int type, XpdRuleConfPO xpdRuleConfPO) {
        // 获取维度结果
        List<CaliUserDimDTO> results = new ArrayList<>();
        //6.3: 获取用户结果
        if (type == 1){
            // 获取维度结果
            getUserDimResult(orgId, xpdId, calId, userIds, xpdRuleConfPO, results);
        }
        if (type == 2){
            // 获取维度分层结果
            getUserLevelResult(orgId, xpdId, calId, userIds, results);
        }
        if (type == 3){
            // 获取维度指标结果
            getUserIndicatorResult(orgId, xpdId, calId, userIds, xpdRuleConfPO, results);
        }
        return results;
    }

    private void getUserIndicatorResult(String orgId, String xpdId, String calId, List<String> userIds, XpdRuleConfPO xpdRuleConfPO, List<CaliUserDimDTO> results) {
        List<CalimeetResultUserIndicatorPO> userIndicatorPOS = calimeetResultUserIndicatorMapper.selectByCaliMeetIdAndUserIds(
            orgId, calId, userIds);

        if (xpdRuleConfPO.getResultType() == 0){
            // 分值
            userIndicatorPOS.forEach(userIndicatorPO -> {
                CaliUserDimDTO result = new CaliUserDimDTO();
                result.setUserId(userIndicatorPO.getUserId());
                result.setIndicatorId(userIndicatorPO.getSdIndicatorId());
                result.setResult(userIndicatorPO.getScoreValue() != null ? String.valueOf(userIndicatorPO.getScoreValue()) : "");
                results.add(result);
            });
        }
        if (xpdRuleConfPO.getResultType() == 1){
            // 达标率
            userIndicatorPOS.forEach(userIndicatorPO -> {
                CaliUserDimDTO result = new CaliUserDimDTO();
                result.setUserId(userIndicatorPO.getUserId());
                result.setIndicatorId(userIndicatorPO.getSdIndicatorId());
                result.setResult(userIndicatorPO.getQualified() != null && userIndicatorPO.getQualified() == 1 ? "达标" : "不达标");
                results.add(result);
            });
        }
    }

    private void getUserLevelResult(String orgId, String xpdId, String calId, List<String> userIds,
        List<CaliUserDimDTO> results) {
        List<CalimeetResultUserDimPO> userDimPOS = calimeetResultUserDimMapper.selectByCaliMeetIdAndUserIds(
            orgId, calId, userIds);
        List<CaliDmDTO> dmsPOs = getDms(orgId, xpdId, false);
        Map<String, String> dimMap = StreamUtil.list2map(dmsPOs, CaliDmDTO::getDmId, CaliDmDTO::getDmName);
        List<XpdLevelAggDTO> levels = getLevels(orgId, xpdId);
        Map<String, String> levelMap = StreamUtil.list2map(levels, XpdLevelAggDTO::getLevelId, XpdLevelAggDTO::getLevelName);
        // 分值
        userDimPOS.forEach(userDimPO -> {
            if (dimMap.containsKey(userDimPO.getSdDimId())){
                CaliUserDimDTO result = new CaliUserDimDTO();
                result.setUserId(userDimPO.getUserId());
                result.setDim(dimMap.get(userDimPO.getSdDimId()));
                result.setDimId(userDimPO.getSdDimId());
                result.setResult(levelMap.get(userDimPO.getGridLevelId()) != null ? levelMap.get(userDimPO.getGridLevelId()) : "");
                results.add(result);
            }
        });
    }

    private void getUserDimResult(
        String orgId, String xpdId, String calId, List<String> userIds, XpdRuleConfPO xpdRuleConfPO,
        List<CaliUserDimDTO> results) {
        List<CalimeetResultUserDimPO> userDimPOS = calimeetResultUserDimMapper.selectByCaliMeetIdAndUserIds(
            orgId, calId, userIds);
        List<CaliDmDTO> dmsPOs = getDms(orgId, xpdId, true);
        Map<String, String> dimMap = StreamUtil.list2map(dmsPOs, CaliDmDTO::getDmId, CaliDmDTO::getDmName);
        if (xpdRuleConfPO.getResultType() == 0){
            // 分值
            userDimPOS.forEach(userDimPO -> {
                if (dimMap.containsKey(userDimPO.getSdDimId())){
                    CaliUserDimDTO result = new CaliUserDimDTO();
                    result.setUserId(userDimPO.getUserId());
                    result.setDim(dimMap.get(userDimPO.getSdDimId()));
                    result.setDimId(userDimPO.getSdDimId());
                    result.setResult(userDimPO.getScoreValue() != null ? String.valueOf(userDimPO.getScoreValue()) : "");
                    results.add(result);
                }
            });
        }
        if (xpdRuleConfPO.getResultType() == 1){
            // 达标率
            userDimPOS.forEach(userDimPO -> {
                if (dimMap.containsKey(userDimPO.getSdDimId())){
                    CaliUserDimDTO result = new CaliUserDimDTO();
                    result.setUserId(userDimPO.getUserId());
                    result.setDim(dimMap.get(userDimPO.getSdDimId()));
                    result.setDimId(userDimPO.getSdDimId());
                    result.setResult(userDimPO.getQualifiedPtg() != null ? userDimPO.getQualifiedPtg()+"%" : "");
                    results.add(result);
                }
            });
        }
    }

    /**
     * 获取维度分层
     * @param orgId
     * @param xpdId
     * @return
     */
    public List<XpdLevelAggDTO> getLevels(String orgId, String xpdId) {
        //6.3: 获取维度分层
        List<XpdGridLevelPO> gridLevelList = xpdGridLevelMapper.listByXpdId(orgId, xpdId);
        List<XpdLevelAggDTO> resultList = new ArrayList<>();
        for (XpdGridLevelPO xpdGridLevel : gridLevelList) {
            XpdLevelAggDTO res = new XpdLevelAggDTO();
            res.setLevelName(xpdGridLevel.getLevelName());
            res.setOrderIndex(xpdGridLevel.getOrderIndex());
            res.setLevelId(xpdGridLevel.getId());
            resultList.add(res);
        }
        return resultList;
    }

    public List<CaliDmDTO> getDms(String orgId, String xpdId, boolean isTem) {
        XpdCaliCalcFullDto fullDto = xpdResultCalcService.buildXpdCalcFormula(orgId, xpdId);
        log.info("维度规则, {}", BeanHelper.bean2Json(fullDto, JsonInclude.Include.NON_NULL));
        if (fullDto == null){
            return new ArrayList<>();
        }
        Map<String, String> sdDimNameMap = fullDto.getSdDimNameMap();
        if (sdDimNameMap.isEmpty()){
            return new ArrayList<>();
        }
        List<CaliDmDTO> caliDmDTOS = new ArrayList<>();
        // 按维度分层结果导入，不过滤
        List<XpdDimRule4Cali> dmIndicators = fullDto.getRuleList();
        // 按维度导入，过滤, 未配置规则的过滤，按绩效结果的过来掉
        if (isTem){
            dmIndicators = fullDto.getRuleList().stream().filter(rule->
                rule.getCaliCalcType() != XpdDimRule4Cali.TYPE_NONE
                && !(fullDto.getResultType() == 1 && Objects.equals(rule.getCalcType(), DimCalcTypeEnum.PERF_SCORE.getCode()))
                && rule.getCalcType() != DimCalcTypeEnum.PERF_RESULT.getCode()
            ).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(dmIndicators)){
            return Lists.newArrayList();
        }
        for (XpdDimRule4Cali dmIndicator : dmIndicators) {
            String id = dmIndicator.getSdDimId();
            if (sdDimNameMap.containsKey(id)){
                CaliDmDTO caliDmDTO = new CaliDmDTO();
                caliDmDTO.setDmId(id);
                caliDmDTO.setDmName(sdDimNameMap.get(id));
                caliDmDTO.setTotalScore(dmIndicator.getTotalScore());
                caliDmDTOS.add(caliDmDTO);
            }
        }
        return caliDmDTOS;
    }

    public Map<String, XpdSdIndicatorScoreDto> getIndicators(String orgId, String xpdId) {
        XpdCaliCalcFullDto fullDto = xpdResultCalcService.buildXpdCalcFormula(orgId, xpdId);
        if (fullDto == null){
            return new HashMap<>();
        }
        Map<String, XpdSdIndicatorScoreDto> sdIndicatorMap = fullDto.getSdIndicatorMap();
        if (sdIndicatorMap.isEmpty()){
            return new HashMap<>();
        }
        return sdIndicatorMap;
    }

    @NotNull
    private CalimeetPO getCalimeetPO(String orgId, String calId) {
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(calId, orgId);
        if (calimeetPO == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXIST);
        }
        return calimeetPO;
    }


    private List<CaliUserDTO> getCaliMeetUsers(String orgId, String calId, ImportUserDTO importUserDTO) {
        List<CaliUserDTO> caliUserDTOS = new ArrayList<>();
        if (importUserDTO.getIsAll() == 1){
            caliUserDTOS = calimeetUserMapper.getCaliMeetUsers(orgId, calId, null);
            if (CollectionUtils.isEmpty(caliUserDTOS)){
                return Lists.newArrayList();
            }
            return caliUserDTOS;
        }
        if (CollectionUtils.isEmpty(importUserDTO.getUserIds()) && importUserDTO.getIsAll() == 0){
            return caliUserDTOS;
        }
        //6.3: 获取校准会人员
        caliUserDTOS = calimeetUserMapper.getCaliMeetUsers(orgId, calId, importUserDTO.getUserIds());

        List<String> records = caliUserDTOS.stream().map(CaliUserDTO::getLatestRecordId).collect(Collectors.toList());
        List<CalimeetDimResultDto> calimeetDimResultDtos = calimeetRecordMapper.getRecordDetails(orgId, records);
        if (CollectionUtils.isNotEmpty(calimeetDimResultDtos)) {
            Map<String, CalimeetDimResultDto> recordDetailMap =
                StreamUtil.list2map(calimeetDimResultDtos, CalimeetDimResultDto::getUserId);
            for (CaliUserDTO caliUser : caliUserDTOS) {
                CalimeetDimResultDto recordDetail = recordDetailMap.get(caliUser.getUserId());
                if (recordDetail != null) {
                    if (StringUtils.isNotBlank(recordDetail.getCaliDetails())){
                        caliUser.setCalimeetDimResultDto( BeanHelper.json2Bean(recordDetail.getCaliDetails(),CaliUpdateUserResultWrapDto.class));
                    }
                    caliUser.setReason(recordDetail.getReason());
                    caliUser.setSuggestion(recordDetail.getSuggestion());
                }
            }
        }
        return caliUserDTOS;
    }

    public XpdRuleConfPO getXpdRule(String orgId, String caliId){
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliId, orgId);
        if (calimeetPO == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXIST);
        }
        XpdRuleConfPO xpdRuleConfPO = xpdRuleConfMapper.selectByXpdId(orgId,calimeetPO.getXpdId());
        if (xpdRuleConfPO == null){
            throw new ApiException(ExceptionKeys.XPD_RULE_CONF_NOT_EXIST);
        }
        return xpdRuleConfPO;
    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    @Async
    public void saveCaliUserImportData(String orgId, String userId, List<CaliUserImportDbDTO> userResult) {
        // 6.3 保存校准后数据
        if (CollectionUtils.isEmpty(userResult)){
            return;
        }
        log.info("保存校准结果，{}",BeanHelper.bean2Json(userResult, JsonInclude.Include.NON_NULL));
        String caliId = userResult.get(0).getCaliId();
        String lockKey = String.format(RedisKeys.LK_CALI_IMPT, orgId, caliId);
        try {
            talentRedisRepository.setValue(lockKey, "1", 5, TimeUnit.MINUTES);
            List<CaliUserResultContainer> caliUserResultContainers = new ArrayList<>();
            userResult.forEach(userImport -> {
                CaliUserResultContainer caliUserResultContainer = new CaliUserResultContainer();
                caliUserResultContainer.setUserId(userImport.getUserId());
                caliUserResultContainer.setReason(cutStr(userImport.getReason()));
                caliUserResultContainer.setSuggestion(cutStr(userImport.getSuggestion()));
                caliUserResultContainer.setResultList(userImport.getCaliUpdateUserResultReq());
                caliUserResultContainers.add(caliUserResultContainer);
            });
            xpdResultCalcService.batchCalcCaliResult(orgId, userId, caliId, caliUserResultContainers);
        } catch (Exception e) {
            log.warn("保存异常:{}", caliId, e);
        } finally {
            talentRedisRepository.delete(lockKey);
        }
    }

    public String cutStr(String reason) {
        if (reason != null && reason.length() > 2000) {
            reason = reason.substring(0, 2000);
        }
        return reason;
    }
}
