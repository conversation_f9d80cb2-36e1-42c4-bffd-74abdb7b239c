<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetUserPO">
        <!--@mbg.generated-->
        <!--@Table rv_calimeet_user-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="calimeet_id" jdbcType="CHAR" property="calimeetId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="cali_status" jdbcType="TINYINT" property="caliStatus"/>
        <result column="latest_record_id" jdbcType="CHAR" property="latestRecordId"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_id, calimeet_id, user_id, cali_status, latest_record_id, deleted, create_user_id,
        create_time, update_user_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_calimeet_user
        where id = #{id,jdbcType=CHAR}
    </select>
    <select id="getCountByMeetIds"
            resultType="com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserStatisticDTO">
        select calimeet_id AS meetId,count(1) AS userCount from rv_calimeet_user
        where org_id =#{orgId} and deleted=0
        and calimeet_id in
        <foreach collection="caliMeetIds" item="caliMeetId" open="(" close=")" separator=",">
            #{caliMeetId}
        </foreach>
        group by calimeet_id
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from rv_calimeet_user
        where id = #{id,jdbcType=CHAR}
    </delete>
    <update id="delCaliRecord">
        update rv_calimeet_user set cali_status = 0 , latest_record_id=''
        where org_id=#{orgId}
        and calimeet_id=#{caliMeetId}
        and deleted = 0
    </update>
    <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetUserPO">
        <!--@mbg.generated-->
        insert into rv_calimeet_user (id, org_id, calimeet_id,
        user_id, cali_status, latest_record_id,
        deleted, create_user_id, create_time,
        update_user_id, update_time)
        values (#{id,jdbcType=CHAR}, #{orgId,jdbcType=CHAR}, #{calimeetId,jdbcType=CHAR},
        #{userId,jdbcType=CHAR}, #{caliStatus,jdbcType=TINYINT}, #{latestRecordId,jdbcType=CHAR},
        #{deleted,jdbcType=TINYINT}, #{createUserId,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateUserId,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateByPrimaryKey"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetUserPO">
        <!--@mbg.generated-->
        update rv_calimeet_user
        set org_id = #{orgId,jdbcType=CHAR},
        calimeet_id = #{calimeetId,jdbcType=CHAR},
        user_id = #{userId,jdbcType=CHAR},
        cali_status = #{caliStatus,jdbcType=TINYINT},
        latest_record_id = #{latestRecordId,jdbcType=CHAR},
        deleted = #{deleted,jdbcType=TINYINT},
        create_user_id = #{createUserId,jdbcType=CHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user_id = #{updateUserId,jdbcType=CHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=CHAR}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update rv_calimeet_user
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.orgId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="calimeet_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.calimeetId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.userId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="cali_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.caliStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="latest_record_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.latestRecordId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.deleted,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="create_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.createUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.updateUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=CHAR}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rv_calimeet_user
        (id, org_id, calimeet_id, user_id, cali_status, latest_record_id, deleted, create_user_id,
        create_time, update_user_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.calimeetId,jdbcType=CHAR},
            #{item.userId,jdbcType=CHAR}, #{item.caliStatus,jdbcType=TINYINT}, #{item.latestRecordId,jdbcType=CHAR},
            #{item.deleted,jdbcType=TINYINT}, #{item.createUserId,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="selectOverviewStatistic"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetGeneralOverviewVO">
        select count(distinct a.user_id) as needCaliNumber, count(a.latest_record_id != '' or null) as caliNumber
        from rv_calimeet_user a
        JOIN udp_lite_user c on a.user_id = c.user_id and c.org_id = #{orgId} and c.deleted = 0
        where a.calimeet_id = #{caliMeetId}
        and a.org_id = #{orgId}
        and a.deleted = 0
        <if test="query != null and query.openAuth">
            <include refid="auth_fragment" />
        </if>
    </select>

    <select id="countLargeChangePeopleNumber" resultType="int">
        SELECT
            count(distinct a.user_id) as largeChangePeopleNumber
        FROM
            rv_calimeet_user a
        JOIN udp_lite_user c on a.user_id = c.user_id and c.org_id = #{orgId} and c.deleted = 0
        WHERE
            a.calimeet_id = #{caliMeetId}
            AND EXISTS (
                SELECT 1
                FROM rv_calimeet_record b
                JOIN rv_calimeet_record_item c ON b.id = c.calimeet_record_id
                                              AND c.calimeet_id = b.calimeet_id
                                              AND c.deleted = 0
                WHERE b.calimeet_id = #{caliMeetId}
                  AND b.deleted = 0
                  AND b.org_id = #{orgId}
                  AND b.id = a.latest_record_id
                  AND c.cali_shift > #{threshold}
            )
            AND a.org_id = #{orgId}
            AND a.deleted = 0
        <if test="query != null and query.openAuth">
            <include refid="auth_fragment" />
        </if>
    </select>

    <!-- 获取校准前九宫格用户分布数据 -->
    <select id="getBeforeCalibrationDistribution"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetGridItemVO">
        WITH total_users AS (
            SELECT
                COUNT(DISTINCT cu.user_id) AS total_count
            FROM
                rv_calimeet_user cu
            JOIN udp_lite_user c on cu.user_id = c.user_id and c.org_id = #{orgId} and c.deleted = 0
            WHERE
                cu.calimeet_id = #{caliMeetId} AND cu.org_id = #{orgId} AND cu.deleted = 0
            <if test="query != null and query.openAuth">
                <include refid="auth_fragment" />
            </if>
        ),
        grid_users AS (
            SELECT
                gc.id AS cellId,
                gc.cell_index AS cellIndex,
                gc.x_index AS xIndex,
                gc.y_index AS yIndex,
                COUNT(DISTINCT cu.user_id) AS userCount
            FROM
                rv_calimeet_user cu
            JOIN udp_lite_user c on cu.user_id = c.user_id and c.org_id = #{orgId} and c.deleted = 0
            JOIN
                rv_calimeet_result_user_dimcomb ud ON cu.user_id = ud.user_id
                                                AND ud.org_id = #{orgId}
                                                AND ud.xpd_id = #{xpdId}
                                                AND ud.dim_comb_id = #{dimCombId}
                                                AND ud.calimeet_id = cu.calimeet_id
                                                AND ud.deleted = 0
            JOIN
                rv_xpd_grid_cell gc ON ud.cell_id = gc.id
                                   AND gc.org_id = #{orgId}
                                   AND gc.xpd_id = #{xpdId}
                                   AND (gc.dim_comb_id = #{dimCombId} OR gc.dim_comb_id = '')
                                   AND gc.deleted = 0
            WHERE
                cu.calimeet_id = #{caliMeetId}
                AND cu.org_id = #{orgId}
                AND cu.deleted = 0
            <if test="query != null and query.openAuth">
                <include refid="auth_fragment" />
            </if>
            GROUP BY
                gc.id, gc.cell_index, gc.x_index, gc.y_index
        )
        SELECT
            g.cellId,
            g.cellIndex,
            g.xIndex,
            g.yIndex,
            g.userCount,
            IFNULL(ROUND(g.userCount * 100.0 / t.total_count, 2), 0) AS userRatio
        FROM
            grid_users g,
            total_users t
        ORDER BY
            g.cellIndex
    </select>

    <!-- 获取校准后九宫格用户分布数据 -->
    <select id="getAfterCalibrationDistribution"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetGridItemVO">
        WITH total_users AS (
            SELECT
                COUNT(DISTINCT cu.user_id) AS total_count
            FROM
                rv_calimeet_user cu
            JOIN udp_lite_user c on cu.user_id = c.user_id and c.org_id = #{orgId} and c.deleted = 0
            WHERE
                cu.calimeet_id = #{caliMeetId} AND cu.org_id = #{orgId} AND cu.deleted = 0
            <if test="query != null and query.openAuth">
                <include refid="auth_fragment" />
            </if>
        ),
        calibrated_users AS (
            -- 已校准的用户分布
            SELECT
                gc.id AS cellId,
                gc.cell_index AS cellIndex,
                gc.x_index AS xIndex,
                gc.y_index AS yIndex,
                COUNT(DISTINCT cu.user_id) AS userCount
            FROM
                rv_calimeet_user cu
            JOIN udp_lite_user c on cu.user_id = c.user_id and c.org_id = #{orgId} and c.deleted = 0
            JOIN
                rv_calimeet_record_item ri ON cu.user_id = ri.user_id
                                        AND ri.calimeet_id = cu.calimeet_id
                                        AND ri.org_id = #{orgId}
                                        AND ri.dim_comb_id = #{dimCombId}
                                        AND ri.deleted = 0
                                        AND ri.calimeet_record_id = cu.latest_record_id
            JOIN
                rv_xpd_grid_cell gc ON ri.cell_index = gc.cell_index
                                   AND gc.org_id = #{orgId}
                                   AND gc.xpd_id = #{xpdId}
                                   AND (gc.dim_comb_id = #{dimCombId} OR gc.dim_comb_id = '')
                                   AND gc.deleted = 0
            WHERE
                cu.calimeet_id = #{caliMeetId}
                AND cu.org_id = #{orgId}
                AND cu.deleted = 0
                AND cu.latest_record_id IS NOT NULL
            <if test="query != null and query.openAuth">
                <include refid="auth_fragment" />
            </if>
            GROUP BY
                gc.id, gc.cell_index, gc.x_index, gc.y_index
        ),
        uncalibrated_users AS (
            -- 未校准的用户分布
            SELECT
                gc.id AS cellId,
                gc.cell_index AS cellIndex,
                gc.x_index AS xIndex,
                gc.y_index AS yIndex,
                COUNT(DISTINCT cu.user_id) AS userCount
            FROM
                rv_calimeet_user cu
            JOIN udp_lite_user c on cu.user_id = c.user_id and c.org_id = #{orgId} and c.deleted = 0
            JOIN
                rv_calimeet_result_user_dimcomb ud ON cu.user_id = ud.user_id
                                                AND ud.org_id = #{orgId}
                                                AND ud.xpd_id = #{xpdId}
                                                AND ud.dim_comb_id = #{dimCombId}
                                                AND ud.calimeet_id = cu.calimeet_id
                                                AND ud.deleted = 0
            JOIN
                rv_xpd_grid_cell gc ON ud.cell_id = gc.id
                                   AND gc.org_id = #{orgId}
                                   AND gc.xpd_id = #{xpdId}
                                   AND (gc.dim_comb_id = #{dimCombId} OR gc.dim_comb_id = '')
                                   AND gc.deleted = 0
            WHERE
                cu.calimeet_id = #{caliMeetId}
                AND cu.org_id = #{orgId}
                AND cu.deleted = 0
                AND (cu.latest_record_id IS NULL OR cu.latest_record_id = '' OR NOT EXISTS (
                    SELECT 1 FROM rv_calimeet_record_item ri
                    WHERE ri.user_id = cu.user_id
                    AND ri.calimeet_id = cu.calimeet_id
                    AND ri.calimeet_record_id = cu.latest_record_id
                    AND ri.dim_comb_id = #{dimCombId}
                    AND ri.deleted = 0
                ))
            <if test="query != null and query.openAuth">
                <include refid="auth_fragment" />
            </if>
            GROUP BY
                gc.id, gc.cell_index, gc.x_index, gc.y_index
        ),
        combined_results AS (
            -- 合并结果：已校准用户
            SELECT cellId, cellIndex, xIndex, yIndex, userCount FROM calibrated_users

            UNION ALL

            -- 合并结果：未校准用户，但排除已校准的相同格子
            SELECT u.cellId, u.cellIndex, u.xIndex, u.yIndex, u.userCount
            FROM uncalibrated_users u
            WHERE NOT EXISTS (SELECT 1 FROM calibrated_users c WHERE c.cellId = u.cellId)
        ),
        aggregated_results AS (
            -- 聚合相同格子的结果
            SELECT
                cellId,
                cellIndex,
                xIndex,
                yIndex,
                SUM(userCount) AS userCount
            FROM
                combined_results
            GROUP BY
                cellId, cellIndex, xIndex, yIndex
        )
        SELECT
            a.cellId,
            a.cellIndex,
            a.xIndex,
            a.yIndex,
            a.userCount,
            IFNULL(ROUND(a.userCount * 100.0 / t.total_count, 2), 0) AS userRatio
        FROM
            aggregated_results a,
            total_users t
        ORDER BY
            a.cellIndex
    </select>

    <!-- 查询校准幅度较大人员列表 -->
    <select id="listLargeChangePersonnel"
        resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetLargeChangePersonnelVO">
        SELECT
            cu.user_id AS userId,
            ri.cali_shift AS caliShift,
            r.reason AS caliReason,
            bg.cell_name AS originalCellIndexName,
            ag.cell_name AS cellIndexName,
            r.create_user_id AS caliUserId
        FROM
            rv_calimeet_user cu
        JOIN
            udp_lite_user c on cu.user_id = c.user_id and c.org_id = #{orgId} and c.deleted = 0
        JOIN
            rv_calimeet_record r ON cu.latest_record_id = r.id AND r.deleted = 0
        JOIN
            rv_calimeet_record_item ri ON r.id = ri.calimeet_record_id AND ri.deleted = 0
        LEFT JOIN
            rv_xpd_grid_cell bg ON ri.original_cell_index = bg.cell_index
                             AND bg.org_id = #{orgId}
                             AND bg.xpd_id = #{xpdId}
                             AND (bg.dim_comb_id = #{query.dimCombId} OR bg.dim_comb_id = '')
                             AND bg.deleted = 0
        LEFT JOIN
            rv_xpd_grid_cell ag ON ri.cell_index = ag.cell_index
                             AND ag.org_id = #{orgId}
                             AND ag.xpd_id = #{xpdId}
                             AND (ag.dim_comb_id = #{query.dimCombId} OR ag.dim_comb_id = '')
                             AND ag.deleted = 0
        WHERE
            cu.calimeet_id = #{caliMeetId}
            AND cu.org_id = #{orgId}
            AND cu.deleted = 0
            AND ri.dim_comb_id = #{query.dimCombId}
            AND ri.cali_shift > #{threshold}

            <include refid="auth_fragment" />

        ORDER BY
            ri.cali_shift DESC
    </select>

    <!-- 统计校准幅度较大人员总数 -->
    <select id="countLargeChangePersonnel" resultType="int">
        SELECT
            COUNT(DISTINCT cu.user_id)
        FROM
            rv_calimeet_user cu
        JOIN
            rv_calimeet_record r ON cu.latest_record_id = r.id AND r.deleted = 0
        JOIN
            rv_calimeet_record_item ri ON r.id = ri.calimeet_record_id AND ri.deleted = 0
        WHERE
            cu.calimeet_id = #{caliMeetId}
            AND cu.org_id = #{orgId}
            AND cu.deleted = 0
            AND ri.dim_comb_id = #{query.dimCombId}
            AND ri.cali_shift > #{threshold}
    </select>

    <!-- 通用权限 &amp;&amp; 搜索 -->
    <sql id="auth_fragment">
        <!--@ignoreSql-->
        <choose>
            <when test="query.emptyAuth">
                and 1 != 1
            </when>
            <when test="(query.scopeDeptIds != null and query.scopeDeptIds.size() != 0) and (query.scopeUserIds != null and query.scopeUserIds.size() != 0)">
                and (c.dept_id in
                <foreach close=")" collection="query.scopeDeptIds" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
                or c.id in
                <foreach close=")" collection="query.scopeUserIds" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
                )
            </when>
            <when test="(query.scopeDeptIds != null and query.scopeDeptIds.size() != 0)">
                and c.dept_id in
                <foreach close=")" collection="query.scopeDeptIds" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </when>
            <when test="(query.scopeUserIds != null and query.scopeUserIds.size() != 0)">
                and c.id in
                <foreach close=")" collection="query.scopeUserIds" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </when>
        </choose>

        <choose>
            <when test="(query.escapedSearchKey != null and query.escapedSearchKey != '') and (query.searchUserIds != null and query.searchUserIds.size() != 0)">
                and (
                (
                <if test="query.kwType != null and query.kwType == 2">
                    c.username like concat('%', #{query.escapedSearchKey}, '%')
                </if>
                <if test="query.kwType != null and query.kwType == 1">
                    c.fullname like concat('%', #{query.escapedSearchKey}, '%')
                </if>
                <if test="query.kwType == null or (query.kwType != 1 and query.kwType != 2)">
                    (c.username like concat('%', #{query.escapedSearchKey}, '%') or c.fullname like concat('%',
                    #{query.escapedSearchKey}, '%'))
                </if>
                )
                or c.id in
                <foreach close=")" collection="query.searchUserIds" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
                )
            </when>
            <when test="(query.escapedSearchKey != null and query.escapedSearchKey != '')">
                <if test="query.kwType != null and query.kwType == 2">
                    and c.username like concat('%', #{query.escapedSearchKey}, '%')
                </if>
                <if test="query.kwType != null and query.kwType == 1">
                    and c.fullname like concat('%', #{query.escapedSearchKey}, '%')
                </if>
                <if test="query.kwType == null or (query.kwType != 1 and query.kwType != 2)">
                    and (c.username like concat('%', #{query.escapedSearchKey}, '%') or c.fullname like concat('%',
                    #{query.escapedSearchKey}, '%'))
                </if>
            </when>
            <when test="(query.searchUserIds != null and query.searchUserIds.size() != 0)">
                and c.id in
                <foreach close=")" collection="query.searchUserIds" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </when>
        </choose>

        <if test="query.posIds != null and query.posIds.size() != 0">
            and c.position_id in
            <foreach close=")" collection="query.posIds" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="query.gradeIds != null and query.gradeIds.size() != 0">
            and c.grade_id in
            <foreach close=")" collection="query.gradeIds" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="query.userStatus != null and query.userStatus != -1">
            <if test="query.userStatus == 2">
                and c.deleted = 1
            </if>
            <if test="query.userStatus != 2">
                and c.status = #{query.userStatus}
                and c.deleted = 0
            </if>
        </if>

    </sql>

    <!-- 根据条件查询校准人员列表 -->
    <select id="selectCaliMeetUsers" resultType="com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserVO">
        SELECT
            cu.id,
            c.user_id AS userId,
            c.user_id,
            c.fullname,
            c.username,
            c.user_no,
            c.dept_id,
            c.dept_name,
            c.grade_id,
            c.grade_name,
            c.position_id,
            c.position_name,
            IF(c.deleted = 1, 2, c.status) AS status,
            cu.cali_status AS caliStatus,
            cu.latest_record_id AS latestRecordId,
            r.create_time AS caliTime,
            r.create_user_id AS caliUserId,
            (SELECT fullname FROM udp_lite_user_sp WHERE user_id = r.create_user_id) AS caliUserFullName,
            r.cali_details,
            r.result_details,
            ri.cali_shift AS caliShift,
            bg.cell_name AS originalCellIndexName,
            ag.cell_name AS cellIndexName
      FROM
          rv_calimeet_user cu
      JOIN
          rv_calimeet cal ON cu.calimeet_id = cal.id AND cal.deleted = 0
      JOIN
          udp_lite_user_sp c ON cu.user_id = c.user_id AND c.deleted = 0
      LEFT JOIN
          rv_calimeet_record r ON cu.latest_record_id = r.id AND r.deleted = 0

      LEFT JOIN
          rv_calimeet_record_item ri ON r.id = ri.calimeet_record_id AND ri.deleted = 0 AND ri.dim_comb_id = #{query.dimCombId}
      LEFT JOIN
          rv_xpd_grid_cell bg ON ri.original_cell_index = bg.cell_index
                              AND bg.org_id = #{orgId}
                              AND bg.xpd_id = cal.xpd_id
                              AND (bg.dim_comb_id = #{query.dimCombId} OR bg.dim_comb_id = '')
                              AND bg.deleted = 0
      LEFT JOIN
          rv_xpd_grid_cell ag ON ri.cell_index = ag.cell_index
                              AND ag.org_id = #{orgId}
                              AND ag.xpd_id = cal.xpd_id
                              AND (ag.dim_comb_id = #{query.dimCombId} OR ag.dim_comb_id = '')
                              AND ag.deleted = 0

      WHERE
          cu.calimeet_id = #{caliMeetId}
          AND cu.org_id = #{orgId}
          AND cu.deleted = 0
        <if test="query.caliStatus != null">
            AND cu.cali_status = #{query.caliStatus}
        </if>
        <if test="query.cellIndex != null">
            AND (
                <!-- 已有校准记录的指定宫格内的人 -->
                (ri.cell_index = #{query.cellIndex} AND ri.dim_comb_id = #{query.dimCombId})
                OR
                <!-- 没有校准记录，但是有盘点结果的指定宫格内的人 -->
                (
                    NOT EXISTS (
                        SELECT 1 FROM rv_calimeet_record_item tmp 
                        WHERE tmp.user_id = cu.user_id 
                        AND tmp.calimeet_id = cu.calimeet_id 
                        AND tmp.dim_comb_id = #{query.dimCombId}
                        AND tmp.deleted = 0
                    )
                    AND EXISTS (
                        SELECT 1 FROM rv_calimeet_result_user_dimcomb ud
                        JOIN rv_xpd_grid_cell gc ON ud.cell_id = gc.id
                        WHERE ud.user_id = cu.user_id
                        AND ud.calimeet_id = cu.calimeet_id
                        AND ud.dim_comb_id = #{query.dimCombId}
                        AND ud.deleted = 0
                        AND gc.cell_index = #{query.cellIndex}
                        AND gc.org_id = #{orgId}
                        AND gc.xpd_id = cal.xpd_id
                        AND (gc.dim_comb_id = #{query.dimCombId} OR gc.dim_comb_id = '')
                        AND gc.deleted = 0
                    )
                )
            )
        </if>
        <include refid="auth_fragment"/>
      ORDER BY
          cu.user_id DESC
  </select>

  <select id="listNotAddedUsers" resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUser4Add">
      SELECT
          c.user_id,
          c.fullname,
          c.username,
          c.user_no,
          c.dept_id,
          c.dept_name,
          c.grade_id,
          c.grade_name,
          c.position_id,
          c.position_name,
          IF(c.deleted = 1, 2, c.status) AS status,
          (
              SELECT
                  t.level_name
              FROM
                  sprv.rv_xpd_result_user p
              JOIN
                  sprv.rv_xpd_level t ON p.xpd_level_id = t.id AND t.deleted = 0
              WHERE
                  p.org_id = #{orgId}
                  AND p.xpd_id = d.id
                  AND p.user_id = a.user_id
          ) AS xpdLevelName
      FROM
          rv_activity_participation_member a
      JOIN
          udp_lite_user_sp c ON a.user_id = c.user_id AND c.deleted = 0 AND a.org_id = c.org_id
      JOIN
          rv_xpd d ON d.aom_prj_id = a.actv_id AND d.deleted = 0 AND d.org_id = a.org_id
      JOIN
          rv_calimeet e ON e.xpd_id = d.id AND e.deleted = 0 AND e.org_id = d.org_id
      WHERE
          NOT EXISTS (
              SELECT
                  1
              FROM
                  rv_calimeet_user p
              WHERE
                  p.calimeet_id = #{caliMeetId}
                  AND p.org_id = #{orgId}
                  AND p.deleted = 0
                  AND p.user_id = a.user_id
          )
          AND a.org_id = #{orgId}
          AND a.deleted = 0
          AND e.id = #{caliMeetId}
      <include refid="auth_fragment"/>
      ORDER BY
          c.fullname
  </select>

  <update id="logicDeleteCaliMeetUser">
      UPDATE
          rv_calimeet_user
      SET
          deleted = 1,
          update_user_id = #{operatorId},
          update_time = NOW()
      WHERE
          id = #{id}
          AND calimeet_id = #{caliMeetId}
          AND org_id = #{orgId}
          AND deleted = 0
  </update>

    <!-- 批量逻辑删除校准人员 -->
    <update id="batchLogicDeleteCaliMeetUsers">
      UPDATE rv_calimeet_user
      SET deleted        = 1,
          update_user_id = #{operatorId},
          update_time    = NOW()
      WHERE calimeet_id = #{caliMeetId}
        AND org_id = #{orgId}
        AND deleted = 0
      <choose>
        <when test="userIds != null and userIds.size() != 0">
          and user_id in
          <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
        </when>
        <otherwise>
          <!--@ignoreSql-->
          and 1 != 1
        </otherwise>
      </choose>
    </update>

    <!-- 检查用户是否已存在于校准会中 -->
    <select id="checkUserExists" resultType="int">
        SELECT COUNT(1)
        FROM rv_calimeet_user
        WHERE org_id = #{orgId}
        AND calimeet_id = #{caliMeetId}
        AND deleted = 0
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="getCaliMeetUsers" resultType="com.yxt.talent.rv.application.calimeet.dto.CaliUserDTO">
        SELECT cu.user_id AS userId, u.username AS username, u.fullname as fullname, cu.latest_record_id as latestRecordId
        FROM
        rv_calimeet_user cu
        JOIN
        udp_lite_user u ON u.id = cu.user_id and cu.org_id = u.org_id
        WHERE
        cu.calimeet_id = #{caliMeetId}
        AND cu.org_id = #{orgId}
        AND cu.deleted = 0
        AND u.deleted = 0
        <if test="(userIds != null and userIds.size()>0)">
            AND cu.user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="listIdByUserIds" resultType="com.yxt.talent.rv.application.calimeet.dto.CaliUserIdDto">
        select id,user_id from rv_calimeet_user where org_id = #{orgId} and calimeet_id = #{caliMeetId} AND deleted = 0
        and user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>

    <update id="batchUpdateLatestRecord">
        <foreach collection="list" item="item" separator=";">
            update rv_calimeet_user set latest_record_id = #{item.latestRecordId},cali_status = 1 where id = #{item.id}
        </foreach>
    </update>

    <select id="getCaliMeetUserIds" resultType="java.lang.String">
        SELECT cu.user_id AS userId
        FROM rv_calimeet_user cu
        WHERE
        cu.calimeet_id = #{caliMeetId}
        AND cu.org_id = #{orgId}
        AND cu.deleted = 0
    </select>

    <select id="getExistentUserIds" resultType="java.lang.String">
        SELECT user_id
        FROM rv_calimeet_user
        WHERE org_id = #{orgId,jdbcType=CHAR}
        AND calimeet_id = #{caliMeetId,jdbcType=CHAR}
        AND deleted = 0
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="findCaliCellIndexMsg" resultType="com.yxt.talent.rv.application.calimeet.dto.CaliIndexNumDTO">
        SELECT
            cellIndex,
            SUM(cellCount) cellCount
        FROM
            (
                SELECT
                    b.cell_index cellIndex,
                    COUNT(*) cellCount
                FROM
                    rv_calimeet_user a
                    INNER JOIN rv_calimeet_record_item b ON a.org_id = b.org_id
                    AND a.latest_record_id = b.calimeet_record_id
                    INNER JOIN udp_lite_user_sp c ON a.user_id = c.id
                WHERE
                    a.org_id = #{orgId}
                    AND a.calimeet_id = #{caliMeetId}
                    AND a.deleted = 0
                    AND a.cali_status = 1
                    and b.dim_comb_id = #{query.dimCombId}
                <include refid="auth_fragment"/>
                GROUP BY
                    b.cell_index
                UNION ALL
                SELECT
                    d.cell_index cellIndex,
                    COUNT(*) cellCount
                FROM
                    rv_calimeet_user a
                    INNER JOIN rv_calimeet b ON a.org_id = b.org_id
                    AND a.calimeet_id = b.id
                    INNER JOIN rv_xpd_result_user_dimcomb d ON b.org_id = d.org_id
                    AND b.xpd_id = d.xpd_id
                    AND a.user_id = d.user_id
                    and d.dim_comb_id = #{query.dimCombId}
                    INNER JOIN udp_lite_user_sp c ON a.user_id = c.id
                WHERE
                    a.org_id = #{orgId}
                    AND a.calimeet_id = #{caliMeetId}
                    AND a.deleted = 0
                    AND a.cali_status IN (0, 2)
                    <include refid="auth_fragment"/>
                GROUP BY
                    d.cell_index
            ) AS combined
        GROUP BY
            cellIndex
    </select>

    <select id="countByOrgIdAndCalimeetId" resultType="int">
        SELECT
            COUNT(*)
        FROM
            rv_calimeet_user a
            INNER JOIN udp_lite_user_sp c ON a.user_id = c.id
        WHERE
            a.org_id = #{orgId,jdbcType=VARCHAR}
            AND a.calimeet_id = #{calimeetId,jdbcType=VARCHAR}
            AND a.deleted = 0
            <include refid="auth_fragment"/>
    </select>

    <select id="findUserIndex" resultType="com.yxt.talent.rv.application.calimeet.dto.CaliUserIndexDTO">
        SELECT
            a.user_id userId,
            b.original_cell_index originalCellIndex,
            b.cell_index cellIndex
        FROM
            rv_calimeet_user a
            INNER JOIN rv_calimeet_record_item b ON a.org_id = b.org_id
            AND a.latest_record_id = b.calimeet_record_id
            INNER JOIN udp_lite_user_sp c ON a.user_id = c.id
        WHERE
            a.org_id = #{orgId}
            AND a.calimeet_id = #{query.meetingId}
            AND a.deleted = 0
            AND a.cali_status = 1
            AND b.dim_comb_id = #{query.dimCombId}
        <include refid="auth_fragment"/>
        UNION ALL
        SELECT
            a.user_id userId,
            dim.cell_index originalCellIndex,
            dim.cell_index cellIndex
        FROM
            rv_calimeet_user a
            INNER JOIN rv_calimeet b ON a.org_id = b.org_id
            AND a.calimeet_id = b.id
            INNER JOIN rv_xpd_result_user_dimcomb dim ON b.org_id = dim.org_id
            AND b.xpd_id = dim.xpd_id
            AND a.user_id = dim.user_id
            INNER JOIN udp_lite_user_sp c ON a.user_id = c.id
        WHERE
            a.org_id = #{orgId}
            AND a.calimeet_id = #{query.meetingId}
            AND dim.dim_comb_id = #{query.dimCombId}
            AND a.deleted = 0
            AND a.cali_status IN (0, 2)
        <include refid="auth_fragment"/>
    </select>

    <select id="findGridUserPage" resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliCellUserVO">
        WITH da AS (
            SELECT
                a.user_id userId,
                b.original_cell_index originalCellIndex,
                b.cell_index cellIndex
            FROM
                rv_calimeet_user a
                INNER JOIN rv_calimeet_record_item b ON a.org_id = b.org_id
                AND a.latest_record_id = b.calimeet_record_id
            WHERE
                a.org_id = #{orgId}
                AND a.calimeet_id = #{query.meetingId}
                AND b.dim_comb_id = #{query.dimCombId}
                AND a.deleted = 0
                AND a.cali_status = 1
            UNION ALL
            SELECT
                a.user_id userId,
                c.cell_index originalCellIndex,
                c.cell_index cellIndex
            FROM
                rv_calimeet_user a
                INNER JOIN rv_calimeet b ON a.org_id = b.org_id
                AND a.calimeet_id = b.id
                INNER JOIN rv_xpd_result_user_dimcomb c ON b.org_id = c.org_id
                AND b.xpd_id = c.xpd_id
                AND a.user_id = c.user_id
            WHERE
                a.org_id = #{orgId}
                AND a.calimeet_id = #{query.meetingId}
                AND a.deleted = 0
                AND c.dim_comb_id = #{query.dimCombId}
                AND a.cali_status IN (0, 2)
        )
        SELECT
            da.userId,
            c.username,
            c.fullname,
            c.dept_name deptName,
            c.position_name positionName
        FROM
            da
            INNER JOIN udp_lite_user_sp c ON da.userId = c.id
        WHERE
            da.cellIndex = #{query.cellIndex}
        <include refid="auth_fragment"/>
    </select>

    <!-- 获取盘点项目校准概览统计信息 -->
    <select id="selectXpdOverviewStatistic"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetGeneralOverviewVO">
        SELECT
        COUNT(DISTINCT cu.user_id) AS needCaliNumber,
        COUNT(DISTINCT CASE WHEN cu.latest_record_id IS NOT NULL AND cu.latest_record_id != '' THEN cu.user_id END) AS
        caliNumber
        FROM rv_calimeet_user cu
        JOIN rv_calimeet cm ON cu.calimeet_id = cm.id AND cm.deleted = 0
        WHERE cm.xpd_id = #{xpdId}
        AND cu.org_id = #{orgId}
        AND cu.deleted = 0
    </select>

    <!-- 统计盘点项目校准幅度较大人员数量 -->
    <select id="countXpdLargeChangePersonnel" resultType="int">
        SELECT COUNT(DISTINCT cu.user_id)
        FROM rv_calimeet_user cu
        JOIN rv_calimeet cm ON cu.calimeet_id = cm.id AND cm.deleted = 0
        JOIN rv_calimeet_record r ON cu.latest_record_id = r.id AND r.deleted = 0
        JOIN rv_calimeet_record_item ri ON r.id = ri.calimeet_record_id AND ri.deleted = 0
        WHERE cm.xpd_id = #{xpdId}
        AND cu.org_id = #{orgId}
        AND cu.deleted = 0
        AND ri.cali_shift > #{threshold}
    </select>

    <!-- 获取盘点项目校准前九宫格用户分布数据 -->
    <select id="selectXpdGridBeforeCalibration"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetGridItemVO">
        WITH earliest_calimeet_users AS (
            -- 获取每个用户最早加入的校准会对应的结果
            SELECT
                cu.user_id,
                cu.calimeet_id,
                cu.latest_record_id,
                ROW_NUMBER() OVER (PARTITION BY cu.user_id ORDER BY cu.create_time ASC) AS rn
            FROM
                rv_calimeet_user cu
            JOIN
                rv_calimeet cm ON cu.calimeet_id = cm.id AND cm.deleted = 0
            WHERE
                cm.xpd_id = #{xpdId}
                AND cu.org_id = #{orgId}
                AND cu.deleted = 0
        ),
        total_users AS (
            -- 统计总人数（每个用户只计算一次）
            SELECT
                COUNT(DISTINCT ecu.user_id) AS total_count
            FROM
                earliest_calimeet_users ecu
            WHERE
                ecu.rn = 1
        ),
        grid_users AS (
            -- 获取每个格子的用户分布
            SELECT
                gc.id AS cellId,
                gc.cell_index AS cellIndex,
                gc.x_index AS xIndex,
                gc.y_index AS yIndex,
                gc.cell_name AS cellName,
                COUNT(DISTINCT ecu.user_id) AS userCount
            FROM
                earliest_calimeet_users ecu
            JOIN
                rv_calimeet_result_user_dimcomb ud
                    ON ecu.user_id = ud.user_id
                    AND ud.org_id = #{orgId}
                    AND ud.xpd_id = #{xpdId}
                    AND ud.calimeet_id = ecu.calimeet_id
                    AND ud.dim_comb_id = #{dimCombId}
                    AND ud.deleted = 0
            JOIN
                rv_xpd_grid_cell gc
                    ON ud.cell_id = gc.id
                    AND gc.org_id = #{orgId}
                    AND gc.xpd_id = #{xpdId}
                    AND (gc.dim_comb_id = #{dimCombId} OR gc.dim_comb_id = '')
                    AND gc.deleted = 0
            WHERE
                ecu.rn = 1
            GROUP BY
                gc.id,
                gc.cell_index,
                gc.x_index,
                gc.y_index,
                gc.cell_name
        )
        SELECT
            g.cellId,
            g.cellIndex,
            g.xIndex,
            g.yIndex,
            g.cellName,
            g.userCount,
            IFNULL(ROUND(g.userCount * 100.0 / t.total_count, 2), 0) AS userRatio
        FROM
            grid_users g,
            total_users t
        ORDER BY
            g.cellIndex
    </select>

    <!-- 获取盘点项目校准后九宫格用户分布数据 -->
    <select id="selectXpdGridAfterCalibration"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetGridItemVO">
        WITH calibrated_users_total AS (
            -- 统计已校准用户总数（每个用户只计算一次）
            SELECT
                COUNT(DISTINCT cu.user_id) AS total_count
            FROM
                rv_calimeet_user cu
            JOIN
                rv_calimeet cm ON cu.calimeet_id = cm.id AND cm.deleted = 0 AND cm.xpd_id = #{xpdId}
            JOIN
                rv_calimeet_record r ON cu.latest_record_id = r.id AND r.deleted = 0
            JOIN
                rv_calimeet_record_item ri
                    ON ri.dim_comb_id = #{dimCombId}
                    AND ri.deleted = 0
                    AND ri.calimeet_record_id = r.id
            WHERE
                cu.org_id = #{orgId}
                AND cu.deleted = 0
                AND cu.latest_record_id IS NOT NULL
        ),
        calibrated_users_pre AS (
            -- 已校准的用户分布（按最近一次校准记录排序）
            SELECT
                gc.id,
                gc.cell_index,
                gc.x_index,
                gc.y_index,
                gc.cell_name,
                cm.calimeet_name,
                cu.user_id,
                ROW_NUMBER() OVER (PARTITION BY cu.user_id ORDER BY r.create_time DESC, ri.create_time DESC) as rn
            FROM
                rv_calimeet_user cu
            JOIN
                rv_calimeet cm ON cu.calimeet_id = cm.id AND cm.deleted = 0 AND cm.xpd_id = #{xpdId}
            JOIN
                rv_calimeet_record r ON cu.latest_record_id = r.id AND r.deleted = 0
            JOIN
                rv_calimeet_record_item ri
                    ON ri.dim_comb_id = #{dimCombId}
                    AND ri.deleted = 0
                    AND ri.calimeet_record_id = r.id
            JOIN
                rv_xpd_grid_cell gc
                    ON ri.cell_index = gc.cell_index
                    AND gc.org_id = #{orgId}
                    AND gc.xpd_id = #{xpdId}
                    AND (gc.dim_comb_id = #{dimCombId} OR gc.dim_comb_id = '')
                    AND gc.deleted = 0
            WHERE
                cu.org_id = #{orgId}
                AND cu.deleted = 0
                AND cu.latest_record_id IS NOT NULL
        ),
        calibrated_users AS (
            -- 已校准用户的最终分布（只取每个用户的最近一次校准记录）
            SELECT
                a.id AS cellId,
                a.cell_index AS cellIndex,
                a.x_index AS xIndex,
                a.y_index AS yIndex,
                a.cell_name AS cellName,
                a.calimeet_name AS caliMeetName,
                COUNT(DISTINCT a.user_id) AS userCount
            FROM
                calibrated_users_pre a
            WHERE
                a.rn = 1
            GROUP BY
                a.id,
                a.cell_index,
                a.x_index,
                a.y_index,
                a.cell_name,
                a.calimeet_name
        )
        -- 最终结果：只返回已校准用户的分布，计算比例
        SELECT
            c.cellId,
            c.cellIndex,
            c.xIndex,
            c.yIndex,
            c.cellName,
            c.userCount AS userCount,
            IFNULL(ROUND(c.userCount * 100.0 / t.total_count, 2), 0) AS userRatio
        FROM
            calibrated_users c,
            calibrated_users_total t
        ORDER BY
            c.cellIndex
    </select>

    <select id="listXpdLargeChangePersonnelPage"
            resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetLargeChangePersonnelVO">
        SELECT
            cu.user_id AS userId,
            ri.cali_shift AS caliShift,
            r.reason AS caliReason,
            bg.cell_name AS originalCellIndexName,
            ag.cell_name AS cellIndexName,
            r.create_user_id AS caliUserId,
            cu.calimeet_id AS caliMeetId,
            cm.calimeet_name AS caliMeetName
        FROM
            rv_calimeet_user cu
        JOIN
            udp_lite_user c on c.user_id = cu.user_id and c.org_id = cu.org_id and c.deleted = 0
        JOIN
            rv_calimeet cm ON cu.calimeet_id = cm.id AND cm.deleted = 0 AND cm.xpd_id = #{xpdId}
        JOIN
            rv_calimeet_record r ON cu.latest_record_id = r.id AND r.deleted = 0
        JOIN
            rv_calimeet_record_item ri ON r.id = ri.calimeet_record_id AND ri.deleted = 0
        LEFT JOIN
            rv_xpd_grid_cell bg ON ri.original_cell_index = bg.cell_index
                AND bg.org_id = #{orgId}
                AND bg.xpd_id = #{xpdId}
                AND (bg.dim_comb_id = #{query.dimCombId} OR bg.dim_comb_id = '')
                AND bg.deleted = 0
        LEFT JOIN
            rv_xpd_grid_cell ag ON ri.cell_index = ag.cell_index
                AND ag.org_id = #{orgId}
                AND ag.xpd_id = #{xpdId}
                AND (ag.dim_comb_id = #{query.dimCombId} OR ag.dim_comb_id = '')
                AND ag.deleted = 0
        WHERE
            cu.org_id = #{orgId}
            AND cu.deleted = 0
            AND ri.dim_comb_id = #{query.dimCombId}
            AND ri.cali_shift > #{threshold}
        <include refid="auth_fragment" />
        ORDER BY ri.cali_shift DESC
    </select>

    <select id="findLastRecord" resultType="com.yxt.talent.rv.application.calimeet.dto.CaliLastRecordUserDTO">
      select a.user_id userId, b.cali_details caliDetails, b.result_details resultDetails
      from rv_calimeet_user        a
      left join rv_calimeet_record b
                on a.org_id = b.org_id and a.calimeet_id = b.calimeet_id and a.latest_record_id = b.id
      where a.org_id = #{orgId}
        and a.calimeet_id = #{calimeetId}
        and a.user_id = #{userId}
        and a.deleted = 0
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rv_calimeet_user
        WHERE org_id = #{orgId}
        <choose>
            <when test="ids != null and ids.size() != 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>
    <select id="statisticByCaliMeetIds" resultType="com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserGroupDTO">
        select calimeet_id as caliMeetId,count(*) as userCount from rv_calimeet_user where org_id = #{orgId}
        and deleted =0
        and calimeet_id in
        <foreach collection="calimeetIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by calimeet_id
    </select>

    <select id="selectUserIdsByCaliMeetId" resultType="java.lang.String">
        select distinct user_id from rv_calimeet_user where org_id = #{orgId}
        and calimeet_id = #{caliMeetId}
    </select>
    <select id="getCaliCount" resultType="java.lang.Long">
        select count(1) from rv_calimeet_user where org_id = #{orgId}
        and  calimeet_id = #{caliMeetId}
        and deleted = 0
        and cali_status = 1
    </select>
</mapper>
